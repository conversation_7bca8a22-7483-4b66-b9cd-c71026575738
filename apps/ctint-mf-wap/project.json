{"name": "ctint-mf-wap", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/ctint-mf-wap", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/next:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"root": "apps/ctint-mf-wap", "outputPath": "dist/apps/ctint-mf-wap"}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/next:server", "defaultConfiguration": "development", "options": {"buildTarget": "ctint-mf-wap:build", "dev": true, "port": 4700, "host": "0.0.0.0"}, "configurations": {"development": {"buildTarget": "ctint-mf-wap:build:development", "dev": true}, "production": {"buildTarget": "ctint-mf-wap:build:production", "dev": false}}}, "export": {"executor": "@nx/next:export", "options": {"buildTarget": "ctint-mf-wap:build:production"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["coverage/apps/ctint-mf-wap"], "options": {"jestConfig": "apps/ctint-mf-wap/jest.config.ts", "passWithNoTests": true}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/ctint-mf-wap/**/*.{ts,tsx,js,jsx}"]}}}}