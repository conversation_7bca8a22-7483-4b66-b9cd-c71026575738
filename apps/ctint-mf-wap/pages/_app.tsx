/* eslint-disable @next/next/no-sync-scripts */
import { AppProps } from 'next/app';
import Head from 'next/head';
import Script from 'next/script';

import './styles.css';

function CustomApp({ Component, pageProps }: AppProps) {
  return (
    <>
      <Head>
        <title>Single Message Portal</title>
      </Head>
      <Script id="GLOBAL_ENV_VARS">
          {`const GLOBAL_ENV_VARS = ${JSON.stringify(pageProps.publicEnvVars)};`}
      </Script>
      <main className="app">
        <Component {...pageProps} />
      </main>
    </>
  );
}

export default CustomApp;
