import type { NextApiRequest, NextApiResponse } from 'next';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader(
    'Access-Control-Allow-Methods',
    'GET, POST, PUT, DELETE, OPTIONS'
  );
  res.setHeader(
    'Access-Control-Allow-Headers',
    'X-Requested-With, Content-Type, Accept'
  );

  try {
    // If everything is okay, return the user data
    res.status(200).json({
      data: {
        permissions: ['ctint-mf-cpp.application.visit', 'ctint-mf-cpp.application.download'],
      },
      isSuccess: true
    });
  } catch (error) {
    // If decryption fails or sourceId does not match
    res.status(403).json({
      data: null,
      isSuccess: false,
      error: 'Failed to get template data',
    });
  }
}
