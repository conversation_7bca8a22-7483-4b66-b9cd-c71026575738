import { clientConfig } from './clientConfig';
// Import the platform client
// @ts-expect-error: no ts provided by Genesys Cloud
import platformClient from 'purecloud-platform-client-v2/dist/node/purecloud-platform-client-v2.js';

interface IQueue {
  id: string;
  activeUsers: number;
  onQueueUsers: number;
}

/*
 * This presence ID is hardcoded because System presence IDs are hardcoded into Genesys Cloud, can never change, and are not unique to orgs or regions
 * In constrast, Org presences are not hardcoded.
 */
const client = platformClient?.ApiClient?.instance;
client.setEnvironment(platformClient.PureCloudRegionHosts?.ap_southeast_2);
const { clientId, redirectUri } = clientConfig;

const searchApi = new platformClient.SearchApi();
const usersApi = new platformClient.UsersApi();
const analyticsApi = new platformClient.AnalyticsApi();
const tokensApi = new platformClient.TokensApi();
const routingApi = new platformClient.RoutingApi();
const presenceApi = new platformClient.PresenceApi();

const cache: any = {};

export function authenticate(
  targetClientId: string = clientId,
  gcRedirect: string | undefined = redirectUri
) {
  console.log('authenticate', targetClientId, gcRedirect);
  return client
    .loginImplicitGrant(targetClientId, gcRedirect, { state: 'state' })
    .then((data: any) => {
      console.log('data', data);
      return data;
    })
    .catch((err: any) => {
      console.error(err);
    });
}

export function getUserByEmail(email: string) {
  const body = {
    pageSize: 25,
    pageNumber: 1,
    query: [
      {
        type: 'TERM',
        fields: ['email', 'name'],
        value: email,
      },
    ],
  };
  return searchApi.postUsersSearch(body);
}

export async function getQueues(userId: string, skipCache = false) {
  if (skipCache) {
    return usersApi.getUserQueues(userId);
  } else if (cache['queues']) {
    return cache['queues'];
  } else {
    try {
      cache['queues'] = await usersApi.getUserQueues(userId);
      return cache['queues'];
    } catch (err) {
      console.error(err);
    }
  }
}

export function getUserRoutingStatus(userId: string) {
  return usersApi.getUserRoutingstatus(userId);
}

export function logoutUser(userId: string) {
  return Promise.all([
    tokensApi.deleteToken(userId),
    presenceApi.patchUserPresence(userId, 'PURECLOUD', {
      presenceDefinition: { id: clientConfig.offlinePresenceId },
    }),
  ]);
}

export async function logoutUsersFromQueue(queueId: string) {
  routingApi
    .getRoutingQueueMembers(queueId)
    .then((data: any) => {
      return Promise.all(data.entities.map((user: any) => logoutUser(user.id)));
    })
    .catch((err: any) => {
      console.error(err);
    });
}

export function getQueueObservations(queues: IQueue[]) {
  const predicates = queues.map((queue: IQueue) => {
    return {
      type: 'dimension',
      dimension: 'queueId',
      operator: 'matches',
      value: queue.id,
    };
  });
  const body = {
    filter: {
      type: 'or',
      predicates,
    },
    metrics: ['oOnQueueUsers', 'oActiveUsers'],
  };
  return analyticsApi.postAnalyticsQueuesObservationsQuery(body);
}

export async function getUserMe(skipCache = false) {
  if (skipCache) {
    return usersApi.getUsersMe({
      expand: ['routingStatus', 'presence'],
    });
  } else if (cache['userMe']) {
    return cache['userMe'];
  } else {
    try {
      cache['userMe'] = await usersApi.getUsersMe({
        expand: ['routingStatus', 'presence'],
      });
      return cache['userMe'];
    } catch (err) {
      console.error(err);
    }
  }
}

export function getUserDetails(id: string, skipCache = false) {
  if (skipCache) {
    let tempDetails: any = {};
    return usersApi
      .getUser(id)
      .then((userDetailsData: any) => {
        tempDetails = userDetailsData;
        return presenceApi.getUserPresence(id, 'purecloud');
      })
      .then((userPresenceData: any) => {
        tempDetails['presence'] = userPresenceData;
        return tempDetails;
      })
      .catch((err: any) => {
        console.error(err);
      });
  } else if (cache['userDetails']) {
    return cache['userDetails'];
  } else {
    return usersApi
      .getUser(id)
      .then((userDetailsData: any) => {
        cache['userDetails'] = userDetailsData || {};
        return presenceApi.getUserPresence(id, 'purecloud');
      })
      .then((userPresenceData: any) => {
        cache['userDetails']['presence'] = userPresenceData;
        return cache['userDetails'];
      })
      .catch((err: any) => {
        console.error(err);
      });
  }
}
