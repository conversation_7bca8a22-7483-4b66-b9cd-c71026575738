{"name": "ctint-mf-user-admin", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/ctint-mf-user-admin", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/next:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"root": "apps/ctint-mf-user-admin", "outputPath": "dist/apps/ctint-mf-user-admin"}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/next:server", "defaultConfiguration": "development", "options": {"buildTarget": "ctint-mf-user-admin:build", "dev": true, "port": 4000, "host": "0.0.0.0"}, "configurations": {"development": {"buildTarget": "ctint-mf-user-admin:build:development", "dev": true}, "production": {"buildTarget": "ctint-mf-user-admin:build:production", "dev": false}}}, "export": {"executor": "@nx/next:export", "options": {"buildTarget": "ctint-mf-user-admin:build:production"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["coverage/apps/ctint-mf-user-admin"], "options": {"jestConfig": "apps/ctint-mf-user-admin/jest.config.ts", "passWithNoTests": true}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/ctint-mf-user-admin/**/*.{ts,tsx,js,jsx}"]}}}}