import Button from '@cdss-modules/design-system/components/_ui/Button';
import Field from '@cdss-modules/design-system/components/_ui/Field';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import {
  Popup,
  PopupContent,
} from '@cdss-modules/design-system/components/_ui/Popup';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import { useEffect, useMemo, useState } from 'react';
import {
  useEvaluationFormList,
  useRouteHandler,
} from '@cdss-modules/design-system';
import { QmFormList } from '../../types/autoqm';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { fireAssignEvaluationFormTemplate } from '../../lib/api/index';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';

type TAssignEvaluationPopupProps = {
  currentRecordingId: string;
  selectedIntergrations: any[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
};

const AssignEvaluationPopup = ({
  currentRecordingId,
  selectedIntergrations = [
    { id: '1', name: 'Integration 1' },
    { id: '2', name: 'Integration 2' },
  ],
  open,
  onOpenChange,
}: TAssignEvaluationPopupProps) => {
  // 从 context 中获取数据
  const { data } = useEvaluationFormList<QmFormList>();
  const [selectedEvaluation, setSelectedEvaluation] = useState('');
  const [selectedVersion, setSelectedVersion] = useState('');
  const [formTemplateList, setFormTemplateList] = useState<any[]>([]);
  const queryClient = useQueryClient();

  const { t } = useTranslation();

  const { basePath } = useRouteHandler();

  // 调用 API 分配 evaluation form
  const mutation = useMutation({
    mutationFn: async (data: {
      recordingId: string;
      interactionId: string;
      formId: string;
    }) => {
      const res = await fireAssignEvaluationFormTemplate(data, basePath);
      return res.data;
    },
    onSuccess: (data) => {
      if (data && data.isSuccess) {
        // 刷新 qmEvaluationList 数据
        queryClient.invalidateQueries({
          queryKey: ['qmEvaluationList', currentRecordingId],
        });
        // 请求成功后关闭弹窗
        onOpenChange(false);
      }
    },
    onError: (error) => {
      console.log('error: ', error);
    },
  });

  useEffect(() => {
    if (data) {
      const groupedData = data.reduce<Record<string, any>>((acc, item) => {
        const key = `${item.formName}-${item.category}-${item.type}`;
        if (!acc[key]) {
          acc[key] = {
            id: key,
            label: item.formName,
            value: key,
            items: [],
          };
        }
        acc[key].items.push({
          formVersion: item.formVersion,
          formId: item.formId,
        });
        return acc;
      }, {});
      setFormTemplateList(Object.values(groupedData));
    }
  }, [data]);

  // 设置 evaluation form version dropdown 数据
  const formVersionOptions = useMemo(() => {
    const itemVersions =
      formTemplateList.find((item) => item.id === selectedEvaluation)?.items ||
      [];
    return itemVersions.map(
      (item: { formVersion: string; formId: string }, index: number) => {
        const label =
          index === 0 ? `${item.formVersion} (Latest)` : item.formVersion;
        return {
          id: item.formId,
          label,
          value: item.formId,
        };
      }
    );
  }, [selectedEvaluation, formTemplateList]);

  useEffect(() => {
    if (formVersionOptions.length > 0) {
      // 默认选中第一个版本
      setSelectedVersion(formVersionOptions[0].value);
    }
  }, [formVersionOptions]);

  return (
    <Popup
      open={open}
      onOpenChange={onOpenChange}
    >
      <PopupContent
        className="sm:max-w-[800px] shadow-md"
        title={t('ctint-mf-interaction.newEvaluation.title')}
      >
        <div className="flex flex-col gap-y-4 w-full p-4">
          <Field
            title={
              t('ctint-mf-interaction.newEvaluation.selectedInteractions') + ':'
            }
            icon={<Icon name="error" />}
            className="w-full mb-1"
          >
            <div className="flex flex-col gap-y-1">
              {selectedIntergrations?.map((item) => (
                <div
                  key={item.conversationId}
                  className="flex flex-row items-center gap-x-2 font-bold text-body"
                >
                  <Icon name="check" />
                  <span className="flex flex-row items-center gap-x-1">
                    {item.conversationId}
                  </span>
                </div>
              ))}
            </div>
          </Field>
          <Field
            title={t('ctint-mf-interaction.newEvaluation.evaluationForm')}
            icon={<Icon name="error" />}
            className="w-full"
          >
            <Select
              placeholder={t(
                'ctint-mf-interaction.newEvaluation.evaluationFormPlaceholder'
              )}
              mode="single"
              options={formTemplateList}
              showSearch={true}
              isPagination={false}
              onChange={(value) => setSelectedEvaluation(value)}
              value={selectedEvaluation}
            />
          </Field>
          <Field
            title={t('ctint-mf-interaction.newEvaluation.formVersion')}
            icon={<Icon name="error" />}
            className="w-full"
          >
            <Select
              placeholder={t(
                'ctint-mf-interaction.newEvaluation.formVersionPlaceholder'
              )}
              mode="single"
              options={formVersionOptions}
              showSearch={true}
              disabled={formVersionOptions.length < 2}
              isPagination={false}
              onChange={(value) => setSelectedVersion(value)}
              value={selectedVersion}
            />
          </Field>
          <div className="w-full flex flex-row justify-between mt-1">
            <Button
              variant="blank"
              onClick={() => onOpenChange(false)}
            >
              {t('ctint-mf-interaction.newEvaluation.cancel')}
            </Button>
            <Button
              variant="primary"
              onClick={() => {
                if (currentRecordingId) {
                  mutation.mutate({
                    recordingId: currentRecordingId,
                    formId: selectedVersion,
                    interactionId: selectedIntergrations[0].conversationId,
                  });
                }
              }}
            >
              {t('ctint-mf-interaction.newEvaluation.add')}
            </Button>
          </div>
        </div>
      </PopupContent>
    </Popup>
  );
};

export default AssignEvaluationPopup;
