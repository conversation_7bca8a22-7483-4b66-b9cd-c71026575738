import Button from '@cdss-modules/design-system/components/_ui/Button';
import Field from '@cdss-modules/design-system/components/_ui/Field';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import {
  Popup,
  PopupContent,
} from '@cdss-modules/design-system/components/_ui/Popup';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import { DUMMY_EVALUATION_FORMS } from '../../lib/dummy/qa';
import { useEffect, useMemo, useState } from 'react';

type TAssignEvaluationPopupProps = {
  selectedQA?: any;
  open: boolean;
  onOpenChange: (open: boolean) => void;
};

const AssignEvaluationPopup = ({
  selectedQA,
  open,
  onOpenChange,
}: TAssignEvaluationPopupProps) => {
  const [selectedEvaluation, setSelectedEvaluation] = useState('cse-1');
  const [selectedUsers, setSelectedUsers] = useState<any[]>(['e-admin']);

  const handleMultipleOption = (e: any) => {
    const isSelected = e?.target.checked;
    const value = e?.target.value;

    if (isSelected) {
      setSelectedUsers((prev) => [...prev, value]);
    } else {
      setSelectedUsers((prev) => {
        return prev?.filter((pre) => pre !== value);
      });
    }
  };
  const removeAllSelection = () => {
    setSelectedUsers([]);
  };

  const [selectedVersion, setSelectedVersion] = useState('cse-1-1');
  const formVersionOptions = useMemo(() => {
    const versions =
      DUMMY_EVALUATION_FORMS.find((item) => item.id === selectedEvaluation)
        ?.versions || [];
    return versions.map((item, index) => {
      const label = index === 0 ? `${item.label} (Latest)` : item.label;
      return {
        id: item.id,
        label,
        value: item.id,
      };
    });
  }, [selectedEvaluation]);

  useEffect(() => {
    if (formVersionOptions.length > 0) {
      setSelectedVersion(formVersionOptions[0].value);
    }
  }, [formVersionOptions]);

  return (
    <Popup
      open={open}
      onOpenChange={onOpenChange}
    >
      <PopupContent
        className="sm:max-w-[800px] shadow-md"
        title="Edit an evaluation"
      >
        <div className="flex flex-col gap-y-4 w-full p-4">
          <Field
            title="Selected Evaluation:"
            icon={<Icon name="error" />}
            className="w-full mb-1"
          >
            <div className="flex flex-col gap-y-1">
              <div className="flex flex-row items-center gap-x-2 font-bold text-body">
                <Icon name="check" />
                <span className="flex flex-row items-center gap-x-1">
                  {selectedQA.id}
                </span>
              </div>
            </div>
          </Field>
          <Field
            title="Evaluation Form"
            icon={<Icon name="error" />}
            className="w-full"
          >
            <Select
              placeholder="Please select an evaluation form"
              mode="single"
              options={DUMMY_EVALUATION_FORMS}
              showSearch={true}
              onChange={(value) => setSelectedEvaluation(value)}
              value={selectedEvaluation}
            />
          </Field>
          <Field
            title="Form Version"
            icon={<Icon name="error" />}
            className="w-full"
          >
            <Select
              placeholder="Please select an form version"
              mode="single"
              options={formVersionOptions}
              showSearch={true}
              disabled={formVersionOptions.length < 2}
              onChange={(value) => setSelectedVersion(value)}
              value={selectedVersion}
            />
          </Field>
          {/* <Field
            title="Assign to"
            icon={<Icon name="error" />}
            className="w-full"
          >
            <Select
              placeholder="Please select an evaluation form"
              mode="multiple"
              options={[
                {
                  id: 'e-admin',
                  label: 'Admin',
                  value: 'e-admin',
                },
                {
                  id: 'e-sup',
                  label: 'Supervisor',
                  value: 'e-sup',
                },
                {
                  id: 'e-e1',
                  label: 'Evaluator 1',
                  value: 'e-e1',
                },
                {
                  id: 'e-e2',
                  label: 'Evaluator 2',
                  value: 'e-e2',
                },
                {
                  id: 'e-a1',
                  label: 'Agent 1',
                  value: 'e-a1',
                },
                {
                  id: 'e-a2',
                  label: 'Agent 2',
                  value: 'e-a2',
                },
              ]}
              showSearch={true}
              onChange={handleMultipleOption}
              removeAllSelection={() => removeAllSelection()}
              value={selectedUsers}
            />
          </Field> */}
          <div className="w-full flex flex-row justify-between mt-1">
            <Button variant="blank">Cancel</Button>
            <Button
              variant="primary"
              onClick={() => onOpenChange(false)}
            >
              Assign
            </Button>
          </div>
        </div>
      </PopupContent>
    </Popup>
  );
};

export default AssignEvaluationPopup;
