import React from 'react';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import dayjs from 'dayjs';

import Input from '@cdss-modules/design-system/components/_ui/Input';

import DateTimePicker from '@cdss-modules/design-system/components/_ui/DateTimePicker';

import { useRole } from '@cdss-modules/design-system';

import { Select } from '@cdss-modules/design-system/components/_ui/Select';

const operators = [
  {
    id: 'equal to',
    label: 'equal',
    value: 'eq',
  },
  {
    id: 'greater than',
    label: 'greater',
    value: 'ge',
  },
  {
    id: 'less than',
    label: 'less',
    value: 'le',
  },
];

type TFilterInputProps = {
  type: string;
  filterName: any;
  filterValue: any;
  isChecked: boolean;
  operatorValue: any;
  handleOperatorValue: (v: any) => void;
  setFilterInput: (prev: any) => void;
};

export const FilterInput = ({
  type,
  filterName,
  filterValue,
  isChecked,
  operatorValue,
  handleOperatorValue,
  setFilterInput,
}: TFilterInputProps) => {
  const { t } = useTranslation();

  // Global Config
  const { globalConfig } = useRole();
  const mediaSourcePossibleOptions = globalConfig?.recording?.mediaSource ?? [];
  const mediaSourceOptions = [
    {
      key: 'mediaSourceOption-all',
      id: 'mediaSourceOption-all',
      label: t('ctint-mf-interaction.filter.all'),
      value: '_all',
    },
    ...(mediaSourcePossibleOptions?.map((item: string) => {
      const mediaSourceItemKey = `mediaSourceOption-${item}`;
      return {
        key: mediaSourceItemKey,
        id: mediaSourceItemKey,
        label: item,
        value: item,
      };
    }) || []),
  ];

  const mediaTypeOptions = [
    {
      key: 'mediaTypeOption-voice',
      id: 'mediaTypeOption-voice',
      label: t('ctint-mf-interaction.filter.voice'),
      value: 'voice',
    },
    {
      key: 'mediaTypeOption-callback',
      id: 'mediaTypeOption-callback',
      label: t('ctint-mf-interaction.filter.callback'),
      value: 'callback',
    },
    {
      key: 'mediaTypeOption-chat',
      id: 'mediaTypeOption-chat',
      label: t('ctint-mf-interaction.filter.chat'),
      value: 'chat',
    },
    {
      key: 'mediaTypeOption-message',
      id: 'mediaTypeOption-message',
      label: t('ctint-mf-interaction.filter.message'),
      value: 'message',
    },
    {
      key: 'mediaTypeOption-email',
      id: 'mediaTypeOption-email',
      label: t('ctint-mf-interaction.filter.email'),
      value: 'email',
    },
  ];

  if (type === 'mediaType') {
    return (
      <div className="w-full">
        <Select
          mode="single"
          isPagination={false}
          placeholder={t('ctint-mf-interaction.filter.allMediaType')}
          options={mediaTypeOptions}
          value={filterValue ?? ''}
          onChange={(v) => {
            setFilterInput((prev: any) => ({
              ...prev,
              [filterName]: v === '' ? '' : v,
            }));
          }}
        />
      </div>
    );
  }

  if (type === 'mediaSource') {
    return (
      <div className="w-full">
        <Select
          mode="single"
          isPagination={false}
          placeholder={t('ctint-mf-interaction.filter.allMediaSource')}
          options={mediaSourceOptions}
          value={filterValue ?? ''}
          onChange={(v) => {
            setFilterInput((prev: any) => ({
              ...prev,
              [filterName]: v === '_all' ? '' : v,
            }));
          }}
        />
      </div>
    );
  }

  if (type === 'evaluation') {
    return (
      <div className="w-full">
        <Select
          mode="single"
          placeholder="Select type of evaluation"
          options={[
            {
              id: 'evaopt-all',
              label: 'All',
              value: '_all',
            },
            {
              id: 'evaopt-unassigned',
              label: 'Unassigned',
              value: 'unassigned',
            },
            {
              id: 'evaopt-assigned',
              label: 'Assigned',
              value: 'assigned',
            },
            {
              id: 'evaopt-assignedToYou',
              label: 'Assigned to you',
              value: 'assignedToYou',
            },
            {
              id: 'evaopt-evaluated',
              label: 'Evaluted',
              value: 'evaluated',
            },
            {
              id: 'evaopt-release',
              label: 'Released',
              value: 'released',
            },
          ]}
          value={filterValue ?? ''}
          onChange={(v) => {
            setFilterInput((prev: any) => ({
              ...prev,
              [filterName]: v === '_all' ? '' : v,
            }));
          }}
        />
      </div>
    );
  }

  if (type === 'duration') {
    return (
      <div className="flex justify-between items-center gap-4 w-full">
        <Select
          mode="single"
          placeholder={t('ctint-mf-interaction.filter.greaterOrLess')}
          options={operators.map((operator) => ({
            key: operator.id,
            id: operator.id,
            label: t(`ctint-mf-interaction.filter.${operator.label}`),
            value: operator.value,
          }))}
          value={operatorValue}
          onChange={handleOperatorValue}
        />
        <Input
          value={filterValue ?? ''}
          onChange={(v) => {
            setFilterInput((prev: any) => ({
              ...prev,
              [filterName]: v,
            }));
          }}
          disabled={!isChecked}
        />
      </div>
    );
  }

  if (type === 'boolean') {
    return <div className="w-full h-10"></div>;
  }

  if (type === 'datetime') {
    return (
      <DateTimePicker
        date={filterValue ? dayjs(filterValue).toDate() : null}
        onChange={(d) => {
          setFilterInput((prev: any) => ({
            ...prev,
            [filterName]: d ? dayjs(d).toISOString() : '',
          }));
        }}
        disabled={!isChecked}
      />
    );
  }

  return (
    <Input
      value={filterValue ?? ''}
      onChange={(v) => {
        setFilterInput((prev: any) => ({
          ...prev,
          [filterName]: v,
        }));
      }}
      disabled={!isChecked}
      placeholder={`${
        filterName === 'conversationId'
          ? t('ctint-mf-interaction.filter.onlyId')
          : ''
      }`}
    />
  );
};

export default FilterInput;
