import { PageBody, PageRenderer } from '@cdss-modules/design-system';
import Main from '../components/_screen/Main';
import Open from '../components/_screen/Open';
import SendTemplate from '../components/_screen/SendTemplate';
import { basePath } from '../lib/appConfig';
import Queue from '../components/_screen/Queue';
import MainLayout from '../components/_ui/MainLayout';

export const Page = () => {
  return (
    <div className="w-full h-screen">
      <PageRenderer
        routes={[
          {
            path: '/',
            group: 'ctint-mf-msg',
            component: <Main />,
          },
          {
            path: '/email',
            group: 'ctint-mf-msg',
            component: <Main />,
          },
          {
            path: '/voicemail',
            group: 'ctint-mf-msg',
            component: <Main />,
          },
          {
            path: '/queue',
            group: 'ctint-mf-msg',
            component: <Queue />,
          },
          {
            path: '/whatsapp/templates/send',
            group: 'ctint-mf-msg',
            component: <Open />,
          },
          {
            path: '/whatsapp/templates/send-template',
            group: 'ctint-mf-msg',
            component: <SendTemplate />,
          },
        ]}
        basePath={basePath}
      >
        <MainLayout>
          <PageBody basePath={basePath} />
        </MainLayout>
      </PageRenderer>
    </div>
  );
};

export const getServerSideProps = async () => {
  const publicEnvVars = Object.keys(process.env).reduce(
    (publicVars: any, key) => {
      if (key.startsWith('CDSS_PUBLIC_')) {
        publicVars[key] = process.env[key];
      }
      return publicVars;
    },
    {}
  ) as any;
  return {
    props: {
      publicEnvVars,
    },
  };
};

export default Page;
