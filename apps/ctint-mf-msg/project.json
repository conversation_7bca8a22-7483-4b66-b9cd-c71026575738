{"name": "ctint-mf-msg", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/ctint-mf-msg", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/next:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"root": "apps/ctint-mf-msg", "outputPath": "dist/apps/ctint-mf-msg"}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/next:server", "defaultConfiguration": "development", "options": {"buildTarget": "ctint-mf-msg:build", "dev": true, "port": 4100, "host": "0.0.0.0"}, "configurations": {"development": {"buildTarget": "ctint-mf-msg:build:development", "dev": true}, "production": {"buildTarget": "ctint-mf-msg:build:production", "dev": false}}}, "export": {"executor": "@nx/next:export", "options": {"buildTarget": "ctint-mf-msg:build:production"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["coverage/apps/ctint-mf-msg"], "options": {"jestConfig": "apps/ctint-mf-msg/jest.config.ts", "passWithNoTests": true}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/ctint-mf-msg/**/*.{ts,tsx,js,jsx}"]}}}}