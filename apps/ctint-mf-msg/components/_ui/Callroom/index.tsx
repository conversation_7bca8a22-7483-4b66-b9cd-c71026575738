import { Panel, useCDSS } from '@cdss-modules/design-system';
import Avatar from '@cdss-modules/design-system/components/_ui/Avatar';
import CallControlButton from '@cdss-modules/design-system/components/_ui/CallControlButton';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { cn, secondsToFormat } from '@cdss-modules/design-system/lib/utils';
import { useEffect, useState } from 'react';
import dayjs from 'dayjs';
import { Timer } from 'lucide-react';

export type TMsg = {
  time: string;
  speaker: string;
  text: string;
};
const Callroom = () => {
  const [padActive, setPadActive] = useState(false);
  const [muteActive, setMuteActive] = useState(false);
  const [holdActive, setHoldActive] = useState(false);
  const [transferActive, setTransferActive] = useState(false);
  const [consultActive, setConsultActive] = useState(false);
  const [groupActive, setGroupActive] = useState(false);

  const handlePadActive = () => {
    setPadActive(!padActive);
  };

  const handleMuteActive = () => {
    setMuteActive(!muteActive);
  };

  const handleHoldActive = () => {
    setHoldActive(!holdActive);
  };

  const handleTransferActive = () => {
    setTransferActive(!transferActive);
  };

  const handleConsultActive = () => {
    setConsultActive(!consultActive);
  };

  const handleGroupActive = () => {
    setGroupActive(!groupActive);
  };

  const { activeInteraction, updateActiveSAA } = useCDSS();
  useEffect(() => {
    // add click event for .saa-keyword to get the data-saa-id and run updateActiveSAA
    const saaKeywords = document.querySelectorAll('.saa-keyword');
    saaKeywords.forEach((keyword) => {
      keyword.addEventListener('click', (e: any) => {
        const saaId = e.target.getAttribute('data-saa-id');
        if (saaId) {
          updateActiveSAA(saaId);
        }
      });
    });
    return () => {
      saaKeywords.forEach((keyword) => {
        keyword.removeEventListener('click', (e: any) => {
          const saaId = e.target.getAttribute('data-saa-id');
          if (saaId) {
            updateActiveSAA(saaId);
          }
        });
      });
    };
  }, [updateActiveSAA]);

  const [count, setCount] = useState<number>(0);

  // Count the seconds from time to now using dayjs
  const time = activeInteraction?.time;
  useEffect(() => {
    if (!time) return;

    // Function to update the time
    const updateTimer = () => {
      const secToNow = dayjs().diff(dayjs(time), 'second');
      setCount(secToNow);
    };
    updateTimer();
    // Set interval to update time every second
    const intervalId = setInterval(updateTimer, 1000);

    return () => clearInterval(intervalId);
  }, [time]);

  return (
    <Panel
      className="p-4"
      containerClassName="h-full flex flex-col"
    >
      <div className="flex h-0 flex-1 justify-center items-center border-b border-grey-200 pb-4 mb-4">
        <div className="flex flex-col items-center gap-y-2">
          <Avatar
            text={activeInteraction?.customerShortName || 'N/A'}
            textClassName="text-t5 text-black"
            className={cn('size-24 flex-none mb-2')}
          />
          <h2 className="font-bold text-t6">
            {activeInteraction?.customerName || 'Unknown Customer'}
          </h2>
          <h2 className="font-bold text-t6">
            {activeInteraction?.phone || 'Unknown Customer'}
          </h2>
          <div className="flex items-center gap-x-1 mt-2">
            <Timer />
            <div>{secondsToFormat(count)}</div>
          </div>
        </div>
      </div>
      <div className="flex flex-col justify-between w-full gap-10 max-w-[550px] mx-auto py-6">
        <div className="flex justify-between item-center">
          <CallControlButton
            className="justify-self-start bg-transparent hover:bg-transparent"
            icon={
              <Icon
                name="pad"
                size={64}
              />
            }
            label={'Pad'}
            active={padActive}
            handleOnChange={handlePadActive}
          />
          <CallControlButton
            className="bg-transparent hover:bg-transparent"
            icon={
              <Icon
                name="mute"
                size={64}
              />
            }
            label={'Mute'}
            active={muteActive}
            handleOnChange={handleMuteActive}
          />
          <CallControlButton
            className="justify-self-end bg-transparent hover:bg-transparent"
            icon={
              <Icon
                name="hold"
                size={32}
              />
            }
            label={'Hold'}
            active={holdActive}
            handleOnChange={handleHoldActive}
          />
        </div>
        <div className="flex justify-between item-center">
          <CallControlButton
            className="justify-self-start bg-transparent hover:bg-transparent"
            icon={
              <Icon
                name="transfer"
                size={32}
              />
            }
            label={'Transfer'}
            active={transferActive}
            handleOnChange={handleTransferActive}
          />
          <CallControlButton
            className="bg-transparent hover:bg-transparent"
            icon={
              <Icon
                name="ivr"
                size={32}
              />
            }
            label={'IVR'}
            active={consultActive}
            handleOnChange={handleConsultActive}
          />
          <CallControlButton
            className="bg-transparent hover:bg-transparent"
            icon={
              <Icon
                name="conference"
                size={32}
              />
            }
            label={'Conference'}
            active={consultActive}
            handleOnChange={handleConsultActive}
          />
          {/* <CallControlButton
            className="justify-self-end bg-transparent hover:bg-transparent"
            icon={
              <Icon
                name="secondCall"
                size={32}
              />
            }
            label={'Call'}
            active={groupActive}
            handleOnChange={handleGroupActive}
          /> */}
        </div>
        <div className="flex justify-center item-center">
          <CallControlButton
            tooltip={'Disconnect'}
            tooltipPosition="right"
            onClick={() => null}
            icon={
              <Icon
                name="phone-end"
                size={32}
              />
            }
            className={cn(
              'flex-none bg-status-danger hover:border-status-danger hover:bg-status-danger hover:opacity-70 text-white disabled:bg-grey-100 disabled:text-grey-300'
            )}
            // active={activeModal === 'dialpad-panel'}
            handleOnChange={() => null}
          />
        </div>
      </div>
    </Panel>
  );
};

export default Callroom;
