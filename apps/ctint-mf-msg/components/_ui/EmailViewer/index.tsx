import {
  <PERSON><PERSON>,
  DUMMY_EMAIL,
  TEmail,
  Tooltip,
} from '@cdss-modules/design-system';
import { GLOBAL_DATETIME_FORMAT } from '@cdss-modules/design-system/lib/constants';
import { cn } from '@cdss-modules/design-system/lib/utils';
import dayjs from 'dayjs';
import { Forward, Reply, ReplyAll, UserPlus } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import QueueListActionMenu from '../QueueListActionMenu';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import dynamic from 'next/dynamic';
const ReactQuill = dynamic(() => import('react-quill'), { ssr: false });

export type TEmailViewerProps = {
  email?: TEmail;
  fullHeight?: boolean;
};

export function EmailViewer({
  email = DUMMY_EMAIL,
  fullHeight,
}: TEmailViewerProps) {
  const [ready, setReady] = useState(false);
  const adjustEmail = useMemo(() => {
    return {
      ...DUMMY_EMAIL,
      ...email,
    } as TEmail;
  }, [email]);

  const [editMode, setEditMode] = useState<string | null>(null);

  const sendTo = () => {
    let target = '';
    if (editMode === 'reply') {
      target = adjustEmail.from;
    } else if (editMode === 'reply-all') {
      target = adjustEmail.to.join(', ');
    }
    return target;
  };

  useEffect(() => {
    setReady(true);
  }, []);

  if (!ready) {
    return null;
  }

  return (
    <div
      className={cn('flex flex-col', fullHeight ? 'h-full' : 'max-h-[70vh]')}
    >
      <div className="w-full flex justify-between items-center py-3 border-b border-primary-400">
        <div className="text-body font-bold">{adjustEmail.subject}</div>
        <div className="flex items-center gap-x-2">
          {(email as any)?.status === 'WAITING' ? (
            <QueueListActionMenu
              data={email}
              icon={<UserPlus className="size-5" />}
            />
          ) : (
            <>
              <Tooltip
                content="Reply"
                trigger={
                  <button
                    type="button"
                    onClick={() => setEditMode('reply')}
                    className="hover:text-primary-600"
                  >
                    <Reply size={20} />
                  </button>
                }
              />
              <Tooltip
                content="Reply All"
                trigger={
                  <button
                    type="button"
                    onClick={() => setEditMode('reply-all')}
                    className="hover:text-primary-600"
                  >
                    <ReplyAll size={20} />
                  </button>
                }
              />
              <Tooltip
                content="Forward"
                trigger={
                  <button
                    type="button"
                    onClick={() => setEditMode('forward')}
                    className="hover:text-primary-600"
                  >
                    <Forward size={20} />
                  </button>
                }
              />
            </>
          )}
        </div>
      </div>
      <div className="w-full py-3 flex justify-between border-b border-grey-200">
        <div className="w-full flex flex-col gap-y-1">
          <div>
            <strong>From: </strong>
            {editMode ? <><EMAIL></> : `${adjustEmail.from}`}
          </div>
          <div className="flex items-center gap-x-2">
            <strong className={editMode ? 'w-[40px]' : ''}>to: </strong>
            {editMode ? (
              <Input
                type="text"
                size="s"
                value={sendTo()}
              />
            ) : (
              adjustEmail?.to?.join(', ')
            )}
          </div>
          {editMode ? (
            <>
              <div className="flex items-center gap-x-2">
                <strong className="w-[40px]">cc: </strong>
                <Input
                  type="text"
                  size="s"
                />
              </div>
              <div className="flex items-center gap-x-2">
                <strong className="w-[40px]">bcc: </strong>
                <Input
                  type="text"
                  size="s"
                />
              </div>
            </>
          ) : (
            <>
              {adjustEmail?.cc && adjustEmail?.cc?.length > 0 && (
                <div>
                  <strong>cc: </strong>
                  {adjustEmail?.cc?.join(', ')}
                </div>
              )}
            </>
          )}
        </div>
        {!editMode && (
          <div className="whitespace-nowrap italic">
            {dayjs(adjustEmail.date).format(GLOBAL_DATETIME_FORMAT)}
          </div>
        )}
      </div>
      <div
        className={cn('relative py-3 h-0 flex-1 flex flex-col email-previewer')}
      >
        {editMode ? (
          <div className="relative h-0 flex-1 overflow-auto">
            <ReactQuill
              theme="snow"
              className="email-preview h-full"
              value={adjustEmail.body}
              onChange={() => null}
            />
          </div>
        ) : (
          <div
            className={cn('h-0 flex-1 overflow-auto email-preview')}
            dangerouslySetInnerHTML={{ __html: adjustEmail?.body }}
          />
        )}
      </div>
      {editMode && (
        <div className="flex justify-end gap-x-2 py-3">
          <Button
            variant="secondary"
            onClick={() => {
              setEditMode(null);
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={() => {
              setEditMode(null);
            }}
          >
            Send
          </Button>
        </div>
      )}
    </div>
  );
}

export default EmailViewer;
