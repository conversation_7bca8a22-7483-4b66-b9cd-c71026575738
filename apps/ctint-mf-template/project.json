{"name": "ctint-mf-template", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/ctint-mf-template", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/next:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"root": "apps/ctint-mf-template", "outputPath": "dist/apps/ctint-mf-template"}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/next:server", "defaultConfiguration": "development", "options": {"buildTarget": "ctint-mf-template:build", "dev": true, "port": 4000, "host": "0.0.0.0"}, "configurations": {"development": {"buildTarget": "ctint-mf-template:build:development", "dev": true}, "production": {"buildTarget": "ctint-mf-template:build:production", "dev": false}}}, "export": {"executor": "@nx/next:export", "options": {"buildTarget": "ctint-mf-template:build:production"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["coverage/apps/ctint-mf-template"], "options": {"jestConfig": "apps/ctint-mf-template/jest.config.ts", "passWithNoTests": true}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/ctint-mf-template/**/*.{ts,tsx,js,jsx}"]}}}}