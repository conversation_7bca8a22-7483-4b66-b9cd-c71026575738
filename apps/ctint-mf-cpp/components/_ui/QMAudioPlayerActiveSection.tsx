// type TQMAudioPlayerActiveSectionProps = {
//   score: number;
//   status?: string;
//   progress?: string;
// };

import { Tooltip, useQM } from '@cdss-modules/design-system';
import { cn } from '@cdss-modules/design-system/lib/utils';
import { DUMMY_CS_EVALUATION_FORM } from '../../lib/dummy/qa';
import { Frown, Smile } from 'lucide-react';
import { useGlobalAudioPlayer } from 'react-use-audio-player';

const QATag = ({
  qid,
  type,
  target,
  time,
  label,
  desc,
}: {
  qid: string;
  type?: string;
  target: string;
  time: number;
  label: string;
  desc: string;
}) => {
  const { duration } = useGlobalAudioPlayer();
  const { highlightQuestion } = useQM();
  return (
    <button
      onClick={() => highlightQuestion([qid], target)}
      className="absolute transition-all w-[30px] h-[30px] bottom-1/2 mb-[2px] z-30 pointer-events-auto hover:h-[40px] hidden"
      style={{
        left: `${(time / duration) * 100}%`,
      }}
    >
      <div className="absolute left-1/2 bottom-0 -translate-x-1/2 h-full w-px bg-black" />
      <Tooltip
        side="bottom"
        trigger={
          <div
            onClick={() => highlightQuestion([qid], target)}
            className={cn(
              'bg-white absolute left-1/2 top-0 -translate-x-1/2 rounded-full',
              type === 'positive' ? 'text-status-success' : 'text-status-danger'
            )}
          >
            {type === 'positive' ? (
              <Smile
                size={20}
                className="-mb-[2px]"
              />
            ) : (
              <Frown
                size={20}
                className="-mb-[2px]"
              />
            )}
          </div>
        }
        content={`${label}: ${desc}`}
      />
    </button>
  );
};

const QMAudioPlayerActiveSection = () => {
  const { activeQMSection, qaFormAnswers, openedQA, tempTime } = useQM();

  const answers = qaFormAnswers?.[`${openedQA}`] || {};
  const tags = answers?.tags;
  const sectionAnswers = answers?.[activeQMSection || ''] || {};
  const secStart = tempTime?.show
    ? tempTime.startTime
    : sectionAnswers?.duration?.start;
  const secEnd = tempTime?.show
    ? tempTime.endTime
    : sectionAnswers?.duration?.end;

  const form = DUMMY_CS_EVALUATION_FORM;
  const activeSectionInfo = form.questions.find(
    (s) => s.id === activeQMSection
  );
  const activeSectionIndex =
    (form?.questions?.findIndex((s) => s.id === activeQMSection) || 0) + 1;

  const { duration } = useGlobalAudioPlayer();

  return (
    <>
      <div className="absolute top-1/2 -translate-y-1/2 h-[20px] w-full">
        <div
          className={cn(
            'absolute size-full top-0 left-0',
            !sectionAnswers?.duration && !tempTime?.show && 'hidden'
          )}
          style={{
            left: `${(secStart / duration) * 100}%`,
            width: `${((secEnd - secStart) / duration) * 100}%`,
          }}
        >
          <div className="absolute top-0 left-0 size-full bg-primary-300 animate-pulse rounded-lg z-0" />
          <div className="absolute bottom-full left-0 py-1 opacity-50 z-10 whitespace-nowrap text-remark">
            {activeSectionIndex}.{activeSectionInfo?.title}
          </div>
        </div>
      </div>
      <div className="absolute top-1/2 -translate-y-1/2 h-[50px] w-full z-30 pointer-events-none">
        {tags &&
          Object.entries(tags).map(([key, tag]: any) => (
            <QATag
              key={`${tag.time}-${tag.label}-${key}`}
              type={tag.type}
              qid={key}
              target={tag.target}
              time={tag.time}
              label={tag.label}
              desc={tag.desc}
            />
          ))}
      </div>
    </>
  );
};

export default QMAudioPlayerActiveSection;
