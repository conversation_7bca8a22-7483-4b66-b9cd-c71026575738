import { useQM } from '@cdss-modules/design-system';
import { cn } from '@cdss-modules/design-system/lib/utils';
import { Frown, Smile } from 'lucide-react';
import { memo } from 'react';
import { useGlobalAudioPlayer } from 'react-use-audio-player';
import QMAIResultDetails from '../QMAIResultDetails';

type TQMAIResultProps = {
  qid: string;
};

const QMAIResult = memo(({ qid }: TQMAIResultProps) => {
  const {
    qaFormAnswers,
    openedQA,
    highlightScript,
    layoutMode,
    expandedQAResults,
    toggleExpandedQAResult,
  } = useQM();

  const showDetails = expandedQAResults?.includes(qid);

  const tags = qaFormAnswers?.[`${openedQA}`]?.tags;
  const thisTag = tags?.[qid];
  const { seek } = useGlobalAudioPlayer();

  return (
    <div
      className={cn(
        'flex flex-col gap-1 text-remark',
        showDetails && 'bg-primary-100',
        layoutMode === '1' && 'py-1 pl-4 border-l-2 border-grey-200',
        layoutMode === '2' && 'inline-flex'
      )}
    >
      <div className="flex gap-x-2">
        <div
          className={cn(
            'flex gap-x-1 items-center',
            layoutMode === '2' && 'w-[200px]'
          )}
        >
          {thisTag.type === 'positive' ? (
            <>
              <Smile
                className="text-status-success -mt-1"
                size={20}
              />
              Passed - {thisTag.accuracy * 100}% matched
            </>
          ) : (
            <>
              <Frown
                className="text-status-danger"
                size={20}
              />
              Failed - {thisTag.accuracy * 100}% matched
            </>
          )}
        </div>
        <div className="flex gap-x-2 w-[70px]">
          <button
            className="underline text-footnote text-status-info hover:text-primary-600"
            onClick={() => {
              if (showDetails) {
                highlightScript('');
              } else {
                highlightScript(thisTag?.label);
              }
              toggleExpandedQAResult(qid, !showDetails);
            }}
          >
            {showDetails ? 'Collapse' : 'See details'}
          </button>
        </div>
      </div>
      {layoutMode === '1' && <QMAIResultDetails qid={qid} />}
    </div>
  );
});

QMAIResult.displayName = 'QMAIResult';

export default QMAIResult;
