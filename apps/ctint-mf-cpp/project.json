{"name": "ctint-mf-cpp", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/ctint-mf-cpp", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/next:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"root": "apps/ctint-mf-cpp", "outputPath": "dist/apps/ctint-mf-cpp"}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/next:server", "defaultConfiguration": "development", "options": {"buildTarget": "ctint-mf-cpp:build", "dev": true, "port": 4500, "host": "0.0.0.0"}, "configurations": {"development": {"buildTarget": "ctint-mf-cpp:build:development", "dev": true}, "production": {"buildTarget": "ctint-mf-cpp:build:production", "dev": false}}}, "export": {"executor": "@nx/next:export", "options": {"buildTarget": "ctint-mf-cpp:build:production"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["coverage/apps/ctint-mf-cpp"], "options": {"jestConfig": "apps/ctint-mf-cpp/jest.config.ts", "passWithNoTests": true}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/ctint-mf-cpp/**/*.{ts,tsx,js,jsx}"]}}}}