import loadGlobalConfig from '@cdss-modules/design-system/lib/globalConfig';
import { Html, Head, Main, NextScript } from 'next/document'
import Script from 'next/script';
declare global {
    interface Window {
        GLOBAL_ENV_VARS: any;
        GLOBAL_BASE_PATH: any;
        GLOBAL_MF_NAME: any;
    }
}

export default function Document() {
    const mfName = process.env.mfName || '';
    const globalConfig = loadGlobalConfig(mfName) as any;
    const basePath = globalConfig?.microfrontends?.[mfName]?.basepath || '';
    return (
        <Html lang="en">
            <Head>
                <link rel="icon" href={`${basePath}/favicon.ico`} />
                <script id="GLOBAL_ENV"
                    dangerouslySetInnerHTML={{ __html: `window.GLOBAL_BASE_PATH = "${basePath}";window.GLOBAL_MF_NAME = "${mfName}";` }}
                />
            </Head>
            <body>
                <Main />
                <NextScript />
            </body>
        </Html>
    )
}