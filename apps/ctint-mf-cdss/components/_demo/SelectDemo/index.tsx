'use client';

import { useState } from 'react';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';

const emotions = [
  {
    id: 'joy',
    label: 'Joy',
    value: 'Joy',
  },
  {
    id: 'sadness',
    label: 'Sadness',
    value: 'Sadness',
  },
  {
    id: 'anger',
    label: 'Anger',
    value: 'Anger',
  },
  {
    id: 'fear',
    label: 'Fear',
    value: 'Fear',
  },
  {
    id: 'surprise',
    label: 'Surprise',
    value: 'Surprise',
  },
];

const planets = [
  {
    id: 'mercury',
    label: 'Mercury',
    value: 'Mercury',
  },
  {
    id: 'venus',
    label: 'Venus',
    value: 'Venus',
  },
  {
    id: 'earth',
    label: 'Earth',
    value: 'Earth',
  },
  {
    id: 'mars',
    label: 'Mars',
    value: 'Mars',
  },
  {
    id: 'jupiter',
    label: 'Jupiter',
    value: 'Jupiter',
  },
];

const SelectDemo = () => {
  const [singleOption, setSingleOption] = useState<string>('');
  const [disabledOption, setDisabledOption] = useState<string>('');
  const [multipleOption, setMultipleOption] = useState<string[]>([]);

  const handleSingleOption = (e: any) => {
    setSingleOption(e);
  };

  const handleDisabledOption = (e: any) => {
    setDisabledOption(e);
  };

  const handleMultipleOption = (e: any) => {
    const isSelected = e?.target.checked;
    const value = e?.target.value;

    if (isSelected) {
      setMultipleOption([...multipleOption, value]);
    } else {
      setMultipleOption((prev) => {
        return prev?.filter((pre) => pre !== value);
      });
    }
  };

  const removeAllSelection = () => {
    setMultipleOption([]);
  };

  return (
    <div className="flex flex-col gap-4">
      <div>
        <h3 className="mb-2">Single</h3>
        <Select
          placeholder="placeholder"
          mode="single"
          options={emotions}
          //   showSearch={true}
          value={singleOption}
          onChange={handleSingleOption}
        />
      </div>
      <div>
        <h3 className="mb-2">Multiple</h3>
        <Select
          placeholder="placeholder"
          mode="multiple"
          options={planets}
          showSearch={true}
          value={multipleOption}
          onChange={handleMultipleOption}
          removeAllSelection={removeAllSelection}
        />
      </div>
      <div>
        <h3 className="mb-2">Single</h3>
        <Select
          placeholder="placeholder"
          mode="single"
          options={emotions}
          showSearch={true}
          value={disabledOption}
          onChange={handleDisabledOption}
          disabled
        />
      </div>
    </div>
  );
};

export default SelectDemo;
