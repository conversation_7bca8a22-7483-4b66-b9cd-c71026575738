'use client';

import { useState } from 'react';
import Radio from '@cdss-modules/design-system/components/_ui/Radio';

const RadioDemo = () => {
  const [value, setValue] = useState<boolean>(false);

  const handleValue = (e: any) => {
    setValue(e.target.value);
  };

  return (
    <Radio
      id="agree"
      name="except"
      label="Agree"
      value="agree"
      checked={value}
      onChange={handleValue}
    />
  );
};

export default RadioDemo;
