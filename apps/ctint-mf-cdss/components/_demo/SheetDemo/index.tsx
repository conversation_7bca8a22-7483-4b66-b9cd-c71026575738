import Button from '@cdss-modules/design-system/components/_ui/Button';
import { Sheet } from '@cdss-modules/design-system/components/_ui/Sheet';
import Switch from '@cdss-modules/design-system/components/_ui/Switch';

const SheetDemo = () => {
  return (
    <Sheet
      trigger={<Button variant="blank">Open Trigger</Button>}
      headerTitle={'View 1'}
      content={
        <div className="overflow-auto h-full">
          <div className="w-full p-4">
            <Switch />
          </div>
          <div className="w-full p-4">
            <Switch />
          </div>
        </div>
      }
      footer={
        <>
          <Button variant={'blank'}>Save & Apply</Button>
          <Button>Apply</Button>
        </>
      }
    />
  );
};

export default SheetDemo;
