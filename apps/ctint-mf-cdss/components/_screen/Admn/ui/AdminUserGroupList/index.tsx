import {
  DataTable,
  PaginationConfig,
} from '@cdss-modules/design-system/components/_ui/DataTable';
import { useEffect, useState } from 'react';
import { ColumnFilter, Table as TableType } from '@tanstack/react-table';
import { useCDSSAdmin, useRouteHandler } from '@cdss-modules/design-system';
import { AdminUserGruopDetail } from '../AdminUserGroupDetail';
import { Condition } from '@cdss-modules/design-system/components/_ui/FilterComponent';
import { GetGroupList } from '../../../../../lib/api';
import { TAdminGroupData } from '@cdss/types/group';
import { useTranslation } from 'react-i18next';
import { generateColumns } from './generateColumns';
export type TAdminUserGroupData = {};
export type AdminUserListProps = {
  cols: Record<string, Condition>;
  customFilterValue: ColumnFilter[];
  setFilterOpen: (isOpen: boolean) => void;
};

export type AdminFilterProps = {
  customApplyFilter: (data: any) => void;
  customClearFilter: () => void;
  cols: Record<string, Condition>;
};
export type TcustmCol = {
  name: string;
  key: string;
  readOnly: boolean;
  filterType: string;
  require: boolean;
};
export const AdminUserGroupList = ({
  cols,
  customFilterValue,
  setFilterOpen,
}: AdminUserListProps) => {
  const { openedEntity, updateOpenedEntity } = useCDSSAdmin();
  const [rowSelection, setRowSelection] = useState({});
  const [table, setTable] = useState<TableType<TAdminGroupData>>();
  const [sortOrder, setSortOrder] = useState<any>();
  const [userGroupList, setUserGroupList] = useState<TAdminGroupData[]>([]);
  const { basePath } = useRouteHandler();
  const { i18n } = useTranslation();
  const paginationConfig: PaginationConfig = {
    enable: true,
    pageIndex: 0,
    pageSize: Number(localStorage.getItem('pageSize')) || 10,
  };
  useEffect(() => {
    getUserGroupList();
  }, []);

  const getUserGroupList = async () => {
    const result = await GetGroupList(basePath, 'user');
    const userGroupList = flatUserGrouptDataForRole(result?.data?.data);
    setUserGroupList(userGroupList);
  };

  const flatUserGrouptDataForRole = (
    userGroupList: TAdminGroupData[]
  ): TAdminGroupData[] => {
    return userGroupList.map((item) => {
      const roleNames = item.roles?.map((role) => role.name).join(',');
      const userNames = item.users?.map((user) => user.name).join(',');
      return {
        ...item,
        userNames,
        roleNames,
      };
    });
  };

  const showColumns: string[] = [];
  const showColumnsKey: string[] = [];
  const customCols: TcustmCol[] = [];

  Object.entries(cols).forEach(([key, item]) => {
    const custmCol: TcustmCol = {
      name: i18n.language == 'en' ? item.labelEn : item.labelCh,
      key: key,
      readOnly: item.readOnly || false,
      filterType: item.filterType,
      require: item.require || false,
    };
    if (item.active) {
      showColumnsKey.push(key);
      showColumns.push(i18n.language == 'en' ? item.labelEn : item.labelCh);
      customCols.push(custmCol);
    }
  });

  const setOpenedEntity = (entity: any) => {
    if (!entity) {
      updateOpenedEntity(null);
      return;
    }
    updateOpenedEntity({
      type: 'usergroups',
      entity,
    });
    setFilterOpen(false);
  };

  return (
    <div className="px-4 pt-1 pb-6 flex flex-col h-full gap-y-4 overflow-auto">
      {openedEntity && openedEntity?.type === 'usergroups' ? (
        <AdminUserGruopDetail
          showCols={customCols}
          setOpenedEntity={setOpenedEntity}
          getUserGroupList={getUserGroupList}
        />
      ) : (
        <div className="flex-1 h-0">
          <DataTable<TAdminGroupData>
            data={userGroupList}
            columns={
              generateColumns(
                showColumnsKey,
                showColumns,
                [],
                sortOrder,
                (input) => {
                  setSortOrder(input);
                },
                (id?: string) => {
                  setOpenedEntity(
                    userGroupList?.find((d) => d.id === `${id}`) || {}
                  );
                }
              ) as any
            }
            loading={false}
            emptyMessage="No data found"
            rowSelection={rowSelection}
            setRowSelection={setRowSelection}
            onClickRow={(row) => {
              setOpenedEntity(userGroupList?.[row?.index || 0] || {});
            }}
            onTableSetUp={(table) => setTable(table)}
            paginationConfig={paginationConfig}
            customFilterValue={customFilterValue}
            resize={true}
            pageSizeOnChange={(p: number) => {
              localStorage.setItem('pageSize', String(p));
            }}
          />
        </div>
      )}
    </div>
  );
};
export default AdminUserGroupList;
