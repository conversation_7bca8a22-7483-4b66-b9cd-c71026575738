import { Button, useCDSS } from '@cdss-modules/design-system';
import SearchInput from '@cdss-modules/design-system/components/_ui/SearchInput';
import PopoverMenu from '@cdss-modules/design-system/components/_ui/PopoverMenu';
import { cn } from '@cdss-modules/design-system/lib/utils';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import FilterCompoment, {
  Condition,
} from '@cdss-modules/design-system/components/_ui/FilterComponent';
import { useEffect, useState } from 'react';
import { AdminFilterProps } from '../AdminUserList';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { basePath as adminBasePath } from '../../../../../lib/appConfig';
import {
  GetRolesList,
  GetQueueList,
  GetGroupList,
} from '../../../../../lib/api';

export function AdminFilter(props: AdminFilterProps) {
  const { cols, customApplyFilter, customClearFilter } = props;
  const { activeInteraction, openQueueItem } = useCDSS();
  const AVAILABLE_FILTERS = [] as any[];
  const allAvailableColumns = [] as any[];
  const [filterFields, setFilterFields] = useState<any[]>([]);
  const [filterInput, setFilterInput] = useState<any>({});
  const [filterValue, setFilterValue] =
    useState<Record<string, Condition>>(cols);
  const clearPreference = (type: string) => null;
  const savePreference = (type: string) => null;

  const dataOptions = async (
    value: Condition,
    setOptions: (options: unknown) => void
  ) => {
    try {
      let data: any = [];
      switch (value.value) {
        case 'queueNames': {
          const groupResult = await GetQueueList(adminBasePath);
          const groupData = groupResult.data.data;
          data = transformData(groupData);
          break;
        }

        case 'roleNames': {
          const roleResult = await GetRolesList(adminBasePath);
          const roleData = roleResult.data.data;
          data = transformData(roleData);
          break;
        }

        case 'userGroupNames': {
          const groupResult = await GetGroupList(adminBasePath, 'user');
          const groupData = groupResult.data.data;
          data = transformData(groupData);
          break;
        }

        case 'queueGroupNames': {
          const groupResult = await GetGroupList(adminBasePath, 'queue');
          const groupData = groupResult.data.data;
          data = transformData(groupData);
          break;
        }
      }

      setOptions(data);
    } catch (error) {
      console.error('Failed to fetch options:', error);
      // 可以加入錯誤處理邏輯,比如設置空數組或顯示錯誤信息
      setOptions([]);
    }
  };

  // 抽取共用的數據轉換邏輯
  const transformData = (items: any[]) => {
    return items.map((item) => ({
      value: item.name,
      labelEn: item.name,
      labelCh: item.name,
    }));
  };

  const combineFiltersTagName = () => {
    const tags: string[] = [];
    const sortedFilters = [...Object.values(filterValue)];
    sortedFilters.map((item) => {
      const key = item.value;
      if (filterValue[key].checked && filterValue[key].data) {
        tags.push(
          `${i18n.language == 'en' ? filterValue[key].labelEn : filterValue[key].labelCh}: ${filterValue[key].data}`
        );
      }
    });
    return tags;
  };

  const renderTagItems = () => {
    const sortedFilters = Object.values(filterValue);
    return sortedFilters.map((item, i) => {
      const key = item.value;
      if (!filterValue[key].checked || !filterValue[key].data) return null;
      return (
        <div
          key={i}
          className="p-1 mr-1 mt-1 flex flex-row items-center border border-black rounded-[4px] text-[12px] text-black"
        >
          <span className="truncate">
            {(i18n.language === 'en'
              ? filterValue[key].labelEn
              : filterValue[key].labelCh) +
              ':' +
              filterValue[key].data}
          </span>
          <span
            onClick={() => {
              setFilterValue((prev) => {
                const newFilterValues = { ...prev };
                delete newFilterValues[key].data;
                newFilterValues[key].checked = false;
                return newFilterValues;
              });
            }}
            className="ml-1 cursor-pointer"
          >
            <Icon name="cross" />
          </span>
        </div>
      );
    });
  };
  const { t, i18n } = useTranslation();

  useEffect(() => {
    // console.log('filterValue', filterValue);
    //customApplyFilter(filterValue);
  }, [filterValue]);

  const clearFilter = (filterValue: Record<string, Condition>) => {
    Object.values(filterValue).forEach((item) => {
      item.checked = false;
      if ('data' in item) {
        delete item.data;
      }
    });
    customClearFilter();
  };

  return (
    <div
      className={cn(
        'w-full mt-3 px-4 inline-flex flex-row overflow-x-auto',
        openQueueItem && 'hidden'
      )}
    >
      <div className="flex-1 flex flex-row">
        {/* Search Filter Input Component */}
        <SearchInput
          tags={combineFiltersTagName()}
          placeholder={t('ctint-mf-user-admin.filter.name')}
        >
          {/* Filter Operation Popover Content */}
          <section>
            {/* Filter Operation Scroll Block */}
            <section className="max-h-[409px] min-w-[600px] max-w-[800px] overflow-y-auto">
              {/* Popover selected filters items */}
              <section className="p-1">
                {Object.keys(filterValue).length > 0 && (
                  <div className="flex flex-wrap flex-row">
                    {renderTagItems()}
                  </div>
                )}
              </section>
              {/* Popover filter input form */}
              <section className="px-4">
                <h2 className="text-remark font-bold my-2">
                  {t('ctint-mf-user-admin.filter.name')}
                </h2>
                <div className="flex flex-col gap-y-2">
                  <FilterCompoment
                    filterValues={filterValue}
                    setFilterValues={setFilterValue}
                    onRequest={(value, setOptions) => {
                      dataOptions(value, setOptions);
                    }}
                  />
                </div>
              </section>
            </section>
            {/* Filter Items Operation Button */}
            <section className="max-h-[45px] px-4 py-1 flex flex-row-reverse w-full ">
              {/* <Button
                className="mx-1 z-0"
                bodyClassName="py-[0.375rem]"
                variant={'orange'}
                onClick={() => clearPreference('filters')}
                size="s"
              >
                Clear Filter
              </Button>
              <Button
                className="mx-1 z-0"
                bodyClassName="py-[0.375rem]"
                variant={'orange'}
                onClick={() => savePreference('filters')}
                size="s"
              >
                Save Filter
              </Button> */}
            </section>
          </section>
        </SearchInput>
        {/* Search Button */}
        <Button
          className="self-center ml-2"
          bodyClassName="border border-black py-[0.375rem]"
          onClick={() => customApplyFilter(filterValue)}
          size="s"
        >
          {t('ctint-mf-user-admin.filter.search')}
        </Button>
        {/* Clear Tags Button */}
        <Button
          className="self-center ml-2"
          bodyClassName="border border-black py-[0.375rem]"
          onClick={() => clearFilter(filterValue)}
          variant="blank"
          size="s"
        >
          {t('ctint-mf-user-admin.filter.clear')}
        </Button>
      </div>
      {/* Table Operation Menu */}
      <PopoverMenu
        icon={
          <Icon
            name="verticalDots"
            className="self-center justify-end cursor-pointer mx-1 flex-shrink-0 hidden"
            size={23}
          />
        }
      >
        <div className="flex flex-col bg-white shadow-[0px_2px_10px_0px_rgba(0,0,0,0.18)]">
          <button
            className="m-2 flex gap-2 items-center w-full"
            onClick={() => null}
          >
            <Icon name="eye" />
            <span>Add Columns</span>
          </button>
          <div className="w-full h-[1px] bg-black"></div>
          <button
            onClick={() => null}
            className="mx-2 mt-2 flex gap-2 items-center w-full"
          >
            <Icon name="save" />
            <span>Save Columns</span>
          </button>
          <button
            onClick={() => null}
            className="m-2 flex gap-2 items-center w-full"
          >
            <Icon name="cross" />
            <span>Clear Columns</span>
          </button>
        </div>
      </PopoverMenu>
    </div>
  );
}

export default AdminFilter;
