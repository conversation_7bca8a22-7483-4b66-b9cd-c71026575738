import { DataTable } from '@cdss-modules/design-system/components/_ui/DataTable';
import { useCallback, useEffect, useState } from 'react';
import { ColumnDef, Table as TableType } from '@tanstack/react-table';
import { SortingButton } from '@cdss-modules/design-system';
import { GLOBAL_DATETIME_FORMAT } from '@cdss-modules/design-system/lib/constants';
import dayjs from 'dayjs';
import { cn } from '@cdss-modules/design-system/lib/utils';
import { useCDSSAdmin, useRouteHandler } from '@cdss-modules/design-system';
import {
  AuditCondition,
  TAuditLogQueryParams,
  TAuditLogResponse,
} from '../../../../../types/adminAudit';
import Pagination from '@cdss-modules/design-system/components/_ui/Pagination';
import { useTranslation } from 'react-i18next';
import { GetUserAuditLogs } from '../../../../../lib/api';
import { set } from 'lodash';

// Types
type TAdminUserAuditLog = {
  logId?: string;
  userId?: string;
  eventType?: string;
  eventTimestamp?: string;
  ipAddress?: string;
  userAgent?: string;
  browser?: string;
  operatingSystem?: string;
  device?: string;
  failureReason?: string;
  additionalInfo?: string;
};
type TAdminUserAuditLogList = {
  logs: TAdminUserAuditLog[];
  total: number;
};

type TAdminUserAuditLogResp = {
  data: TAdminUserAuditLogList;
  error?: string;
  isSuccess?: boolean;
};

export type AdminUserAuditListProps = {
  cols: Record<string, AuditCondition>;
};

// 生成column , 定义每一行 每一列的内容
const generateColumns = (
  columnKeys: string[],
  columnLabels: string[],
  cols: Record<string, AuditCondition>,
  sortOrder: any,
  setSortOrder: (input: any) => void
) => {
  const formattedColumns = columnKeys.map(
    (customColumn: string, index: number) => {
      const isDate = customColumn === 'eventTimestamp';
      return {
        id: customColumn,
        accessorKey: customColumn,
        header: () => {
          return (
            // 如果 cols 包含 customColumn
            cols[customColumn]?.sort ? (
              <SortingButton
                sorting={
                  sortOrder?.[customColumn]
                    ? sortOrder?.[customColumn] === 'ASC'
                      ? 'asc'
                      : 'desc'
                    : false
                }
                onClick={async () => {
                  const targetSortOrder =
                    sortOrder?.[customColumn] === 'ASC' ? 'DESC' : 'ASC';
                  setSortOrder({
                    [customColumn]: targetSortOrder,
                  });
                }}
              >
                {columnLabels[index]}
              </SortingButton>
            ) : (
              <div className="inline-flex items-center gap-x-2">
                <div className="inline-flex">{columnLabels[index]}</div>
              </div>
            )
          );
        },
        cell: ({ row }) => {
          let val = row.getValue(customColumn) as any;
          if (isDate) val = dayjs(val).format(GLOBAL_DATETIME_FORMAT);
          return <div>{val != '' && val != undefined ? val : '——'}</div>;
        },
      } as ColumnDef<TAdminUserAuditLog>;
    }
  );

  return [...formattedColumns];
};

// 定义AdminAuditList 组件
export const AdminAuditList = ({ cols }: AdminUserAuditListProps) => {
  const { t, i18n } = useTranslation();
  // const { audits } = useCDSSAdmin();
  // const dummyData = audits as TAdminUserAuditLogResp;
  const [tableCols, setTableCols] =
    useState<Record<string, AuditCondition>>(cols);
  const [rowSelection, setRowSelection] = useState({});
  const [table, setTable] = useState<TableType<TAdminUserAuditLog>>();
  const { basePath } = useRouteHandler();
  const [auditLogs, setAuditLogs] = useState<TAdminUserAuditLog[]>([]);
  const [sortOrder, setSortOrder] = useState<any>();

  // Pagination
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [perPage, setPerPage] = useState<number>(50);
  const [totalCount, setTotalCount] = useState<number>(0);
  const totalPages = Math.ceil(totalCount / perPage);
  const handleNext = () => {
    let tar = currentPage + 1;
    if (tar > totalPages) tar = totalPages;
    setCurrentPage(tar);
  };

  const handlePrevious = () => {
    let tar = currentPage - 1;
    if (tar < 1) tar = 1;
    setCurrentPage(tar);
  };

  // 定义获取auditlog 的方法
  const getAuditLogsFunc = useCallback(async () => {
    try {
      // 处理filter的参数
      const payload = Object.keys(tableCols).reduce<Record<string, any>>(
        (acc, key) => {
          if (tableCols[key].checked && tableCols[key].data) {
            if (
              key === 'eventTimestamp' &&
              typeof tableCols[key].data === 'object'
            ) {
              const start = (tableCols[key].data as Record<string, string>)
                ?.start;
              const end = (tableCols[key].data as Record<string, string>)?.end;
              if (start || end) {
                acc[key] = {
                  ...(start && { start }),
                  ...(end && { end }),
                };
              }
            } else {
              acc[key] = tableCols[key].data;
            }
          }
          return acc;
        },
        {}
      );
      // 处理sort的参数,将sort的参数加入到paylaod
      if (sortOrder) {
        payload.sort = sortOrder;
      }
      // 处理page的参数
      payload.page = currentPage;
      // 处理perPage的参数
      payload.pageSize = perPage;

      // console.log('payload', payload);

      const result = await GetUserAuditLogs(
        basePath,
        payload as TAuditLogQueryParams
      );
      const respData: TAuditLogResponse = result.data;
      // console.log('result data', respData);
      // 增加异常处理,超时处理

      if (respData.error) {
        alert(respData.error);
      } else {
        setAuditLogs(respData.data.logs);
        setTotalCount(respData.data.total);
      }
    } catch (error) {
      // 错误处理
      if (error instanceof Error) {
        if (error.message === 'Request timeout') {
          alert('Request timed out. Please try again.');
        } else {
          alert('Failed to fetch audit logs. Please try again later.');
        }
      }
    }
  }, [basePath, tableCols, currentPage, perPage, sortOrder]);

  // 调用api 获取数据
  useEffect(() => {
    // 调用api 获取数据
    getAuditLogsFunc();
  }, [getAuditLogsFunc]);

  // 调用api 获取数据
  useEffect(() => {
    // 调用api 获取数据
    setTableCols(cols);
    setCurrentPage(1);
  }, [cols]);

  // 设置显示的column 以及 读取 api 的哪个key
  const showColumnLabels: string[] = [];
  const showColumnKeys: string[] = [];

  Object.entries(tableCols).forEach(([key, item]) => {
    // console.log(key, item.labelEn, item.labelCh);
    showColumnKeys.push(key);
    const label = i18n.language == 'en' ? item.labelEn : item.labelCh;
    showColumnLabels.push(label);
  });

  return (
    <div
      className={cn(
        'px-4 pt-1 pb-6 flex flex-col h-full gap-y-4 overflow-auto'
      )}
    >
      <div className="flex-1 h-0">
        <DataTable<TAdminUserAuditLog>
          data={auditLogs}
          columns={
            generateColumns(
              showColumnKeys,
              showColumnLabels,
              tableCols,
              sortOrder,
              (input) => {
                setSortOrder(input);
                setCurrentPage(1);
              }
            ) as any
          }
          loading={false}
          emptyMessage="No data found"
          // error={error?.message}
          rowSelection={rowSelection}
          setRowSelection={setRowSelection}
          onClickRow={(row) => {
            // console.log(row);
          }}
          onTableSetUp={(table) => setTable(table)}
          resize={true}
        />
      </div>
      {totalPages > 0 && (
        <section className="flex-row">
          <div>
            <Pagination
              current={currentPage}
              perPage={perPage}
              total={totalPages}
              totalCount={totalCount}
              onChange={(v) => setCurrentPage(v)}
              handleOnPrevious={() => handlePrevious()}
              handleOnNext={() => handleNext()}
              handlePerPageSetter={(p: number) => {
                const pageSize = Number(p);
                if (!isNaN(pageSize)) {
                  setPerPage(pageSize);
                }
                setCurrentPage(1);
              }}
            />
          </div>
        </section>
      )}
    </div>
  );
};

export default AdminAuditList;
