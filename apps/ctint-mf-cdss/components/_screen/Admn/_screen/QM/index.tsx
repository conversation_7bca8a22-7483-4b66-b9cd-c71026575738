/* eslint-disable @nx/enforce-module-boundaries */

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Tabs,
  <PERSON>bs<PERSON>ontent,
  toast,
  useRoute<PERSON><PERSON><PERSON>,
} from '@cdss-modules/design-system';
import Field from '@cdss-modules/design-system/components/_ui/Field';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import {
  fireGetFormOptions,
  fireGetMetaDataMapping,
  fireGetStandScriptResult,
  fireUpdateStandardScriptScore,
  fireUploadDictionary,
} from '@cdss/lib/api';
import {
  QueryClient,
  QueryClientProvider,
  useMutation,
  useQuery,
} from '@tanstack/react-query';
import clsx from 'clsx';
import { PenSquare, Save } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { Controller, FormProvider, useForm } from 'react-hook-form';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { CommonPermission } from '@cdss-modules/design-system/@types/CommonPermission';
import { useRole } from '@cdss-modules/design-system';
import { usePermission } from '@cdss-modules/design-system/context/PremissionContext';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';

// 定义 thisData 的类型
interface StandardScriptData {
  standardScriptId: string;
  stepId: string;
  scenarioId: string;
  content: string;
  doSimilarity: boolean;
  similarityRequired: number;
  // 添加其他必要的字段
}

export const QMAdminBody = () => {
  const { basePath } = useRouteHandler();
  const [sopInEditmode, setSopInEditmode] = useState(false);
  const fileInputRef = useRef<any>();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [formNameOptions, setFormNameOptions] = useState<any[]>([]);
  const [formVersionOptions, setFormVersionOptions] = useState<any[]>([]);
  const [selectedFormName, setSelectedFormName] = useState<string>('');
  const [selectedFormVersion, setSelectedFormVersion] = useState<string>('');

  const { globalConfig } = useRole();
  const { permissions } = usePermission();
  const qmAdminPermission = new CommonPermission(globalConfig, permissions);

  const { t } = useTranslation();

  const { data: formOptions } = useQuery({
    queryKey: ['formOptions'],
    queryFn: () =>
      fireGetFormOptions(basePath)?.then((res) => res?.data?.data || []),
  });

  const { data: standardScriptDatas, isFetching: loadingStandardScriptDatas } =
    useQuery({
      queryKey: ['standardScriptDatas', selectedFormVersion],
      queryFn: () =>
        fireGetStandScriptResult(selectedFormVersion, basePath)?.then((res) => {
          return res?.data?.data || [];
        }),
    });

  useEffect(() => {
    if (formOptions && formOptions.length > 0) {
      const groupedData: Record<
        string,
        { id: string; label: string; value: string; items: any[] }
      > = formOptions.reduce((acc: Record<string, any>, item: any) => {
        const key = `${item.formName}-${item.category}-${item.type}`;
        if (!acc[key]) {
          acc[key] = {
            id: key,
            label: item.formName,
            value: key,
            items: [],
          };
        }
        acc[key].items.push({
          formVersion: item.formVersion,
          formId: item.formId,
        });
        return acc;
      }, {});
      setFormNameOptions(Object.values(groupedData));
    }
  }, [formOptions]);

  useEffect(() => {
    const itemVersions =
      formNameOptions.find((item) => item.id === selectedFormName)?.items || [];
    const formVersionOptions = itemVersions.map(
      (item: { formVersion: string; formId: string }, index: number) => {
        const label =
          index === 0 ? `${item.formVersion} (Latest)` : item.formVersion;
        return {
          id: item.formId,
          label,
          value: item.formId,
        };
      }
    );
    setFormVersionOptions(formVersionOptions);
  }, [selectedFormName]);

  useEffect(() => {
    if (formVersionOptions.length > 0) {
      // 默认选中第一个版本
      setSelectedFormVersion(formVersionOptions[0].value);
    }
  }, [formVersionOptions]);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // 调用 API 更新 SOP 评分标准
  const mutation = useMutation({
    mutationFn: async (
      data: {
        standardScriptId: string;
        similarityRequired: number;
      }[]
    ) => {
      const res = await fireUpdateStandardScriptScore(basePath, data);
      return res.data;
    },
    onSuccess: (data) => {
      setSopInEditmode(false);
      if (data && data.isSuccess) {
        // 刷新 standardScriptDatas 数据
        queryClient.invalidateQueries({
          queryKey: ['standardScriptDatas', selectedFormVersion],
        });
        // 请求成功后弹窗
        toast({
          variant: 'success',
          title: 'Score Updated',
          description: `The score for SOP: ELI Public Offer updated successfully!`,
        });
      }
    },
    onError: (error) => {
      console.log('error: ', error);
      toast({
        variant: 'error',
        title: 'Error',
        description: `There is an error: ${error}`,
      });
    },
  });

  // 调用 API upload dictionary
  const dictionaryMutation = useMutation({
    mutationFn: async (formData: FormData) => {
      const res = await fireUploadDictionary(basePath, formData);
      return res.data;
    },
    onSuccess: (data) => {
      console.log('data: ', data);
      toast({
        variant: 'success',
        title: 'Dictionary Uploaded',
        description: `Dictionary has been successfully changed the by your uploaded file!`,
      });
      setSelectedFile(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    },
    onError: (error) => {
      console.log('error: ', error);
      toast({
        variant: 'error',
        title: 'Dictionary Upload Failed',
        description: `There is an error: ${error}`,
      });
    },
  });

  const methods = useForm();
  const {
    handleSubmit,
    control,
    formState: { errors, dirtyFields },
    watch,
  } = methods;
  const onSubmit = async (data: any) => {
    // 过滤掉未改变的输入值
    const filteredData = Object.keys(dirtyFields).reduce(
      (acc: any, key: string) => {
        acc[key] = data[key];
        return acc;
      },
      {}
    );
    if (Object.keys(filteredData).length === 0) {
      window.alert(`No changes detected`);
      return;
    }
    // 将 filteredData 转换为新的对象数组
    const updatedDataArray = Object.entries(filteredData).map(
      ([key, value]) => ({
        standardScriptId: key,
        similarityRequired: Number(value),
      })
    );
    mutation.mutate(updatedDataArray);
  };
  const handleFileSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (selectedFile) {
      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append(
        'version',
        formOptions.find((item: any) => item.formId === selectedFormVersion)
          ?.formVersion || ''
      ); // 添加 version 字段, 固定为 v1
      formData.append('formId', selectedFormVersion);
      formData.append('queueName', 'common_queue'); // 添加 queueName 字段, 固定为 common_queue
      console.log('Form Data:', formData.get('formId'));
      // You can now send formData to your API
      dictionaryMutation.mutate(formData);
    }
  };

  const {
    data: metaDataMappingDatas,
    isFetching: loadingMetaDataMappingDatas,
  } = useQuery({
    queryKey: ['metaDataMappingDatas', selectedFormVersion],
    queryFn: () =>
      fireGetMetaDataMapping(selectedFormVersion, basePath)?.then((res) => {
        return res?.data?.data || [];
      }),
    enabled: !!selectedFormVersion,
    refetchOnWindowFocus: false,
  });

  return (
    <Panel containerClassName="h-full overflow-hidden">
      <Tabs
        defaultTab={'sop'}
        triggers={[
          {
            value: 'sop',
            label: t('ctint-mf-cdss.qmAdmin.tabs.sop'),
          },
          {
            value: 'dictionary',
            label: t('ctint-mf-cdss.qmAdmin.tabs.dictionary'),
          },
          {
            value: 'meta_data_mapping',
            label: t('ctint-mf-cdss.qmAdmin.tabs.metaDataMapping'),
          },
        ]}
      >
        <TabsContent
          value={'sop'}
          className="p-0 h-full flex-1 flex flex-col"
        >
          <FormProvider {...methods}>
            <section className="grid sm:grid-cols-2 grid-cols-1 gap-4 p-6 ">
              <Field
                title={t('ctint-mf-cdss.qmAdmin.evaluationForm')}
                icon={<Icon name="error" />}
                className="w-full"
              >
                <Select
                  placeholder={t(
                    'ctint-mf-cdss.qmAdmin.evaluationFormPlaceholder'
                  )}
                  mode="single"
                  options={formNameOptions}
                  showSearch={true}
                  isPagination={false}
                  onChange={(value) => setSelectedFormName(value)}
                  value={selectedFormName}
                />
              </Field>
              <Field
                title={t('ctint-mf-cdss.qmAdmin.formVersion')}
                icon={<Icon name="error" />}
                className="w-full"
              >
                <Select
                  placeholder={t(
                    'ctint-mf-cdss.qmAdmin.formVersionPlaceholder'
                  )}
                  mode="single"
                  options={formVersionOptions}
                  showSearch={true}
                  disabled={formVersionOptions?.length < 2}
                  isPagination={false}
                  onChange={(value) => setSelectedFormVersion(value)}
                  value={selectedFormVersion}
                />
              </Field>
            </section>
            <form
              className="p-6 mb-4 flex flex-col gap-4 overflow-y-auto"
              onSubmit={handleSubmit(onSubmit)}
            >
              <div className="flex justify-between items-center">
                <h2 className="text-t5 font-bold">
                  {t('ctint-mf-cdss.qmAdmin.sop.title')}
                </h2>
                {qmAdminPermission.isPermissionEnabled(
                  'ctint-mf-admin',
                  'qm',
                  'edit'
                ) && (
                  <div className="flex items-center gap-2">
                    <Button
                      hidden={!sopInEditmode}
                      variant="secondary"
                      onClick={() => {
                        setSopInEditmode(false);
                      }}
                    >
                      {t('ctint-mf-cdss.qmAdmin.sop.back')}
                    </Button>
                    <Button
                      type="submit"
                      hidden={!sopInEditmode}
                      beforeIcon={
                        <Save
                          size={20}
                          className="mr-2"
                        />
                      }
                    >
                      {t('ctint-mf-cdss.qmAdmin.sop.save')}
                    </Button>
                    <Button
                      onClick={() => {
                        setSopInEditmode(true);
                      }}
                      beforeIcon={
                        <PenSquare
                          size={20}
                          className="mr-2"
                        />
                      }
                      hidden={sopInEditmode}
                    >
                      {t('ctint-mf-cdss.qmAdmin.sop.edit')}
                    </Button>
                  </div>
                )}
              </div>

              <h3 className="text-t6 font-bold">
                {t('ctint-mf-cdss.qmAdmin.sop.changeScore')}
              </h3>

              <div className="flex flex-col gap-4 flex-1 h-0 overflow-auto pr-4">
                {loadingStandardScriptDatas ? (
                  <div className="flex justify-center items-center h-full">
                    <Loader size={64} />
                  </div>
                ) : (
                  <table className="text-left">
                    <tr className="border-b border-grey-100 text-center">
                      <th className="py-2 truncate">
                        {t('ctint-mf-cdss.qmAdmin.sop.columns.step')}
                      </th>
                      <th className="py-2 truncate">
                        {t('ctint-mf-cdss.qmAdmin.sop.columns.scenarioId')}
                      </th>
                      <th className="py-2 truncate">
                        {t('ctint-mf-cdss.qmAdmin.sop.columns.content')}
                      </th>
                      <th className="py-2 truncate ">
                        {t(
                          'ctint-mf-cdss.qmAdmin.sop.columns.doSentenceSimilarity'
                        )}
                      </th>
                      <th className="py-2 truncate">
                        {t('ctint-mf-cdss.qmAdmin.sop.columns.scoreToPassed')}
                      </th>
                    </tr>
                    {standardScriptDatas && standardScriptDatas.length > 0 ? (
                      standardScriptDatas?.map(
                        (thisData: StandardScriptData) => {
                          const key = `${thisData.standardScriptId}`;
                          return (
                            <tr
                              key={`sop-step-${key}`}
                              className="border-b border-grey-100"
                            >
                              <td className="py-2 text-center">
                                {thisData.stepId || '-'}
                              </td>
                              <td className="py-2 text-center">
                                {thisData.scenarioId || '-'}
                              </td>
                              <td className="py-2 max-w-[400px]">
                                {thisData.content || '-'}
                              </td>
                              <td className="py-2 text-center">
                                {thisData.doSimilarity
                                  ? t('ctint-mf-cdss.qmAdmin.sop.yes')
                                  : t('ctint-mf-cdss.qmAdmin.sop.no')}
                              </td>
                              <td className="py-2 text-center ">
                                {thisData.doSimilarity && sopInEditmode ? (
                                  <div className="flex items-center justify-center">
                                    <Controller
                                      name={key}
                                      control={control}
                                      defaultValue={thisData.similarityRequired}
                                      render={({ field }) => (
                                        <>
                                          <Input
                                            {...field}
                                            type="text"
                                            className="text-center"
                                            placeholder="Enter Rule Key"
                                            onChange={(value) => {
                                              const inputValue =
                                                value.toString();
                                              // 使用正则表达式限制输入为最多两位小数
                                              if (
                                                inputValue &&
                                                /^\d*\.?\d{0,2}$/.test(
                                                  inputValue
                                                )
                                              ) {
                                                field.onChange(value);
                                              }
                                            }}
                                            onBlur={(e) => {
                                              const value = parseFloat(
                                                e.target.value
                                              );
                                              if (!isNaN(value)) {
                                                const decimalPlaces =
                                                  value.toString().split('.')[1]
                                                    ?.length || 0;
                                                if (decimalPlaces > 2) {
                                                  // 只有当小数位大于2时才格式化为两位小数
                                                  field.onChange(
                                                    value.toFixed(2)
                                                  );
                                                }
                                              }
                                            }}
                                            size="s"
                                          />
                                        </>
                                      )}
                                    />
                                  </div>
                                ) : (
                                  <span>
                                    {thisData.similarityRequired || '--'}
                                  </span>
                                )}
                              </td>
                            </tr>
                          );
                        }
                      )
                    ) : (
                      <tr>
                        <td
                          colSpan={5}
                          className="py-2 text-center"
                        >
                          {t('ctint-mf-cdss.qmAdmin.emptyData')}
                        </td>
                      </tr>
                    )}
                  </table>
                )}
              </div>
            </form>
          </FormProvider>
        </TabsContent>
        <TabsContent
          value={'dictionary'}
          className="p-0 h-0 flex-1 flex flex-col"
        >
          <section className="grid sm:grid-cols-2 grid-cols-1 gap-4 p-6 ">
            <Field
              title={t('ctint-mf-cdss.qmAdmin.evaluationForm')}
              icon={<Icon name="error" />}
              className="w-full"
            >
              <Select
                placeholder={t(
                  'ctint-mf-cdss.qmAdmin.evaluationFormPlaceholder'
                )}
                mode="single"
                options={formNameOptions}
                showSearch={true}
                isPagination={false}
                onChange={(value) => setSelectedFormName(value)}
                value={selectedFormName}
              />
            </Field>
            <Field
              title={t('ctint-mf-cdss.qmAdmin.formVersion')}
              icon={<Icon name="error" />}
              className="w-full"
            >
              <Select
                placeholder={t('ctint-mf-cdss.qmAdmin.formVersionPlaceholder')}
                mode="single"
                options={formVersionOptions}
                showSearch={true}
                disabled={formVersionOptions?.length < 2}
                isPagination={false}
                onChange={(value) => setSelectedFormVersion(value)}
                value={selectedFormVersion}
              />
            </Field>
          </section>
          <div className="p-6">
            <h2 className="mb-4 text-t5 font-bold">
              {t('ctint-mf-cdss.qmAdmin.dictionary.title')}
            </h2>
            <h3 className="mb-4 text-t6 font-bold">
              {`${t('ctint-mf-cdss.qmAdmin.dictionary.changeByUpload')} :`}
            </h3>
            <form onSubmit={handleFileSubmit}>
              <div className={clsx(selectedFile ? 'hidden' : 'relative')}>
                {qmAdminPermission.isPermissionEnabled(
                  'ctint-mf-admin',
                  'qm',
                  'edit'
                ) && (
                  <Button
                    type="button"
                    onClick={() => {
                      if (fileInputRef.current) {
                        fileInputRef.current.click();
                      }
                    }}
                  >
                    {t('ctint-mf-cdss.qmAdmin.dictionary.uploadDictionary')}
                  </Button>
                )}
                <input
                  ref={fileInputRef}
                  hidden
                  type="file"
                  onChange={handleFileChange}
                />
              </div>
              {selectedFile && (
                <div className="mt-4 p-4 border max-w-[800px] border-gray-300 rounded">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-sm font-medium">
                        {t('ctint-mf-cdss.qmAdmin.dictionary.fileName')}:{' '}
                        {selectedFile.name}
                      </p>
                      <p className="text-sm">
                        {t('ctint-mf-cdss.qmAdmin.dictionary.fileSize')}:{' '}
                        {(selectedFile.size / 1024).toFixed(2)} KB
                      </p>
                    </div>
                    <div className="flex items-center gap-x-2">
                      <Button
                        size="s"
                        type="submit"
                        disabled={selectedFormVersion === ''}
                      >
                        {t('ctint-mf-cdss.qmAdmin.dictionary.submit')}
                      </Button>
                      <Button
                        size="s"
                        type="button"
                        variant="secondary"
                        onClick={handleRemoveFile}
                      >
                        {t('ctint-mf-cdss.qmAdmin.dictionary.remove')}
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </form>
          </div>
        </TabsContent>
        <TabsContent
          value={'meta_data_mapping'}
          className="p-0 h-0 flex-1 flex flex-col"
        >
          <section className="grid sm:grid-cols-2 grid-cols-1 gap-4 p-6 ">
            <Field
              title={t('ctint-mf-cdss.qmAdmin.evaluationForm')}
              icon={<Icon name="error" />}
              className="w-full"
            >
              <Select
                placeholder={t(
                  'ctint-mf-cdss.qmAdmin.evaluationFormPlaceholder'
                )}
                mode="single"
                options={formNameOptions}
                showSearch={true}
                isPagination={false}
                onChange={(value) => setSelectedFormName(value)}
                value={selectedFormName}
              />
            </Field>
            <Field
              title={t('ctint-mf-cdss.qmAdmin.formVersion')}
              icon={<Icon name="error" />}
              className="w-full"
            >
              <Select
                placeholder={t('ctint-mf-cdss.qmAdmin.formVersionPlaceholder')}
                mode="single"
                options={formVersionOptions}
                showSearch={true}
                disabled={formVersionOptions?.length < 2}
                isPagination={false}
                onChange={(value) => setSelectedFormVersion(value)}
                value={selectedFormVersion}
              />
            </Field>
          </section>
          <div className="p-6 overflow-y-auto">
            <h2 className="mb-4 text-t5 font-bold">
              {t('ctint-mf-cdss.qmAdmin.metaDataMapping.title')}
            </h2>
            <div className="w-full overflow-auto">
              {loadingMetaDataMappingDatas ? (
                <div className="flex justify-center items-center h-full">
                  <Loader size={64} />
                </div>
              ) : (
                <table className="text-left w-full">
                  <tr className="border-b border-grey-100">
                    <th className="py-2 truncate">
                      {t('ctint-mf-cdss.qmAdmin.metaDataMapping.key')}
                    </th>
                    <th className="py-2 truncate">
                      {t('ctint-mf-cdss.qmAdmin.metaDataMapping.value')}
                    </th>
                    <th className="py-2 truncate">
                      {t('ctint-mf-cdss.qmAdmin.metaDataMapping.display')}
                    </th>
                  </tr>
                  {metaDataMappingDatas && metaDataMappingDatas.length > 0 ? (
                    metaDataMappingDatas.map((item: any) => (
                      <tr
                        key={item.id}
                        className="border-b border-grey-100"
                      >
                        <td>{item.keyDefinition}</td>
                        <td>{item.value}</td>
                        <td>{item.displayValue}</td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td
                        colSpan={3}
                        className="py-2 text-center"
                      >
                        {t('ctint-mf-cdss.qmAdmin.emptyData')}
                      </td>
                    </tr>
                  )}
                </table>
              )}
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </Panel>
  );
};

// Create a client
const queryClient = new QueryClient();

const QMAdminScreen = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <QMAdminBody />
    </QueryClientProvider>
  );
};

export default QMAdminScreen;
