import * as React from 'react';
import { useEditor } from '@grapesjs/react';
import Input from '@cdss-modules/design-system/components/_ui/Input';

import type { Trait } from 'grapesjs';
import { cx } from './common';
import { Button } from '@cdss-modules/design-system';
import Checkbox from '@cdss-modules/design-system/components/_ui/Checkbox';

interface StylePropertyFieldProps extends React.HTMLProps<HTMLDivElement> {
  trait: Trait;
}

export default function TraitPropertyField({
  trait,
  ...rest
}: StylePropertyFieldProps) {
  const editor = useEditor();
  const handleChange = (value: string) => {
    trait.setValue(value);
  };

  const onChange = (ev: any) => {
    handleChange(ev.target.value);
  };

  const handleButtonClick = () => {
    const command = trait.get('command');
    if (command) {
      typeof command === 'string'
        ? editor.runCommand(command)
        : command(editor, trait);
    }
  };

  const type = trait.getType();
  const defValue = trait.getDefault() || trait.attributes.placeholder;
  const value = trait.getValue();
  // const valueWithDef = typeof value !== 'undefined' ? value : defValue;

  let inputToRender = (
    <Input
      placeholder={defValue}
      value={value}
      onChange={(ev: any) => {
        handleChange(ev);
      }}
    />
  );

  switch (type) {
    case 'select':
      {
        inputToRender = (
          <select
            value={value}
            onChange={onChange}
          >
            {trait.getOptions().map((option) => (
              <option
                key={trait.getOptionId(option)}
                value={trait.getOptionId(option)}
              >
                {trait.getOptionLabel(option)}
              </option>
            ))}
          </select>
        );
      }
      break;
    case 'color':
      {
        inputToRender = (
          <input
            placeholder={defValue}
            value={value}
            onChange={onChange}
          />
        );
      }
      break;
    case 'checkbox':
      {
        inputToRender = (
          <Checkbox
            id={value}
            label={''}
            value={value}
            checked={value}
            onChange={(ev) => trait.setValue(ev.target.checked)}
          />
        );
      }
      break;
    case 'button':
      {
        inputToRender = (
          <Button
            fullWidth
            onClick={handleButtonClick}
          >
            {trait.getLabel()}
          </Button>
        );
      }
      break;
  }

  return (
    <div
      {...rest}
      className={cx('mb-3 px-1 w-full')}
    >
      <div className={cx('flex mb-2 items-center')}>
        <div className="flex-grow capitalize font-bold">
          {trait.getLabel()}:
        </div>
      </div>
      {inputToRender}
    </div>
  );
}
