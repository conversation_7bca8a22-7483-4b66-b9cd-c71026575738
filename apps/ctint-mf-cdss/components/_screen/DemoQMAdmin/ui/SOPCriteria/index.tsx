import React from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import Field from '@cdss-modules/design-system/components/_ui/Field';
import { DUMMY_DICTIONARY, DUMMY_META } from '../../dummy';
import { Trash2 } from 'lucide-react';
const criteriaTypeOptions = [
  // { label: 'Step Result', value: 'step-result' },
  { label: 'Metadata', value: 'metadata' },
  { label: 'Dictionary', value: 'dict' },
  { label: 'Rule Set', value: 'rule-set' },
];

const relationOptions = [
  { label: 'Exists', value: 'exists' },
  { label: 'Equals to', value: 'equals' },
  { label: '≥', value: 'ge' },
  { label: '>', value: 'gt' },
  { label: '≤', value: 'le' },
  { label: '<', value: 'lt' },
];
const ruleSetRelationOptions = [
  { label: 'All', value: 'all' },
  { label: 'Number of', value: 'numberof' },
];
const StepOptions = [{ label: 'Step 1', value: 'step 1', id: 'step1' }];
const StepValueOptions = [
  { label: 'Passed', value: 'passed', id: 'passed' },
  { label: 'Failed', value: 'failed', id: 'failed' },
];

const metaOptions = DUMMY_META.map((meta) => ({
  label: meta.name,
  value: meta.metaKey,
  id: meta.id,
}));

const dictionaryOptions = DUMMY_DICTIONARY.map((dict) => ({
  label: dict.name,
  value: dict.entityKey,
  id: dict.id,
}));

const SOPCriteria = ({ control, path, index, remove, fields }: any) => {
  const { watch } = useFormContext();
  const dataType = watch(`${path}.type`);
  const dataRelation = watch(`${path}.relation`);

  const isCompareValue =
    dataRelation === 'equals' ||
    dataRelation === 'ge' ||
    dataRelation === 'gt' ||
    dataRelation === 'le' ||
    dataRelation === 'lt';
  return (
    <div className="flex flex-col gap-2 mb-4 w-full">
      <div className="w-full flex items-center gap-4">
        <div className="flex gap-4 items-center">
          <div className="w-6 font-bold">{`C${index + 1}.`}</div>
          <Field
            title=""
            className="flex-1"
          >
            <Controller
              name={`${path}.type`}
              control={control}
              rules={{ required: 'Type is required' }}
              render={({ field }) => (
                <Select
                  {...field}
                  labelClassName="h-full text-remark"
                  labelContainerClassName="h-8"
                  options={criteriaTypeOptions?.map((option) => ({
                    id: option.value,
                    label: option.label,
                    value: option.value,
                  }))}
                  placeholder="Select Type"
                />
              )}
            />
          </Field>
          {dataType === 'step-result' ? (
            <>
              <Field title="">
                <Controller
                  name={`${path}.step`}
                  control={control}
                  rules={{ required: 'Step is required' }}
                  render={({ field }) => (
                    <Select
                      {...field}
                      labelClassName="h-full text-remark"
                      labelContainerClassName="h-8 w-[120px]"
                      options={StepOptions}
                      placeholder="Step"
                    />
                  )}
                />
              </Field>
              <span>is</span>
              <Field title="">
                <Controller
                  name={`${path}.dataValue`}
                  control={control}
                  rules={{ required: 'Step Result is required' }}
                  defaultValue={relationOptions[0].value}
                  render={({ field }) => (
                    <Select
                      {...field}
                      labelClassName="h-full text-remark"
                      labelContainerClassName="h-8"
                      options={StepValueOptions}
                      placeholder="Step Result"
                    />
                  )}
                />
              </Field>
              {dataRelation === 'equals' && (
                <Field title="">
                  <Controller
                    name={`${path}.dataValue`}
                    control={control}
                    rules={{ required: 'Data value is required' }}
                    render={({ field }) => (
                      <Input
                        {...field}
                        placeholder="Enter Data Value"
                      />
                    )}
                  />
                </Field>
              )}
            </>
          ) : (
            <>
              {dataType === 'metadata' && (
                <Field title="">
                  <Controller
                    name={`${path}.key`}
                    control={control}
                    rules={{ required: 'Metadata is required' }}
                    render={({ field }) => (
                      <Select
                        {...field}
                        labelClassName="h-full text-remark"
                        labelContainerClassName="h-8"
                        showSearch={true}
                        options={metaOptions}
                        placeholder="Metadata"
                      />
                    )}
                  />
                </Field>
              )}
              {dataType === 'dict' && (
                <Field title="">
                  <Controller
                    name={`${path}.key`}
                    control={control}
                    rules={{ required: 'Dictionary is required' }}
                    render={({ field }) => (
                      <Select
                        {...field}
                        showSearch={true}
                        options={dictionaryOptions}
                        placeholder="Dictionary"
                      />
                    )}
                  />
                </Field>
              )}
              <Field title="">
                <Controller
                  name={`${path}.relation`}
                  control={control}
                  rules={{ required: 'Relation is required' }}
                  defaultValue={relationOptions[0].value}
                  render={({ field }) => (
                    <Select
                      {...field}
                      labelClassName="h-full text-remark"
                      labelContainerClassName="h-8"
                      options={(dataType === 'rule-set'
                        ? ruleSetRelationOptions
                        : relationOptions
                      )?.map((option) => ({
                        id: option.value,
                        label: option.label,
                        value: option.value,
                      }))}
                      placeholder="Relation"
                    />
                  )}
                />
              </Field>
              {dataType === 'rule-set' && dataRelation === 'numberof' && (
                <>
                  <Field
                    title=""
                    className="w-[80px]"
                  >
                    <Controller
                      name={`${path}.relationFactor`}
                      control={control}
                      rules={{
                        required: 'Number of rules is required',
                        min: 1,
                      }}
                      render={({ field }) => (
                        <Input
                          {...field}
                          type="number"
                          placeholder="Num."
                          min="1"
                        />
                      )}
                    />
                  </Field>
                </>
              )}
              {dataType === 'rule-set' && (
                <>
                  <div>of</div>
                  <div className="w-[200px]">
                    <Controller
                      name={`${path}.ruleSet`}
                      control={control}
                      render={({ field }) => (
                        <Select
                          {...field}
                          placeholder="Select rules"
                          mode="multiple"
                          labelClassName="h-full text-remark"
                          labelContainerClassName="h-8"
                          showSearch={true}
                          options={
                            new Array(fields?.length || 0)
                              ?.fill(0)
                              ?.filter((_, i) => i !== index)
                              ?.map((_, i) => ({
                                id: `Rule C${i + 1}`,
                                label: `Rule C${i + 1}`,
                                value: `Rule C${i + 1}`,
                              })) || []
                          }
                          onChange={(e) => {
                            const isSelected = e?.target.checked;
                            const value = e?.target.value;
                            const prevValues = field?.value || [];

                            if (isSelected) {
                              field?.onChange([...prevValues, value]);
                            } else {
                              field?.onChange(
                                prevValues?.filter(
                                  (prev: string) => prev !== value
                                )
                              );
                            }
                          }}
                          value={field?.value || []}
                        />
                      )}
                    />
                  </div>
                  <div>passed.</div>
                </>
              )}
              {isCompareValue && (
                <Field title="">
                  <Controller
                    name={`${path}.dataValue`}
                    control={control}
                    rules={{ required: 'Data value is required' }}
                    render={({ field }) => (
                      <Input
                        {...field}
                        placeholder="Enter Data Value"
                      />
                    )}
                  />
                </Field>
              )}
            </>
          )}
        </div>
        <button
          type="button"
          onClick={() => remove(index)}
        >
          <Trash2 size={20} />
        </button>
      </div>
    </div>
  );
};

export default SOPCriteria;
