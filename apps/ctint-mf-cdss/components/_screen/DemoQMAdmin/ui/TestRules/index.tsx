import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import Field from '@cdss-modules/design-system/components/_ui/Field';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';

import { Controller, useForm, FormProvider } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { cn } from '@cdss-modules/design-system/lib/utils';
import { DUMMY_SOP } from '../../dummy';
import { Button } from '@cdss-modules/design-system';
import { Check, X } from 'lucide-react';
import { useState } from 'react';

const dummySop = DUMMY_SOP;

const dummySopOptions =
  dummySop?.map((sop) => ({
    label: sop.sop.name,
    value: sop.id,
    id: sop.id,
  })) || [];

const DUMMY_CATEGORY = [
  { label: 'Stage 1', value: 'category1', id: 'category1' },
  { label: 'Stage 2', value: 'category2', id: 'category2' },
];

const DUMMY_STEP = [
  { label: 'Step 1', value: 'step1', id: 'step1' },
  { label: 'Step 2', value: 'step2', id: 'step2' },
];

export type TSOP = {
  sop: string;
  category: string;
  step: string;
  text: string;
};

const testSopSchema = yup
  .object({
    sop: yup.string(),
    category: yup.string(),
    step: yup.string(),
    text: yup.string(),
  })
  .required();

export const TestRules = () => {
  const methods = useForm({
    resolver: yupResolver(testSopSchema),
  });
  const {
    handleSubmit,
    control,
    formState: { errors },
  } = methods;
  const [showResult, setShowResult] = useState(false);

  const onSubmit = async (data: any) => {
    setShowResult(true);
  };

  return (
    <FormProvider {...methods}>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="flex flex-col w-full h-full p-6 overflow-auto"
      >
        <div className="flex-1 h-0 flex flex-col gap-y-4">
          <div className={cn('flex gap-x-4 w-full')}>
            <div className="w-full">
              <Field
                title="SOP"
                titleClassName="mb-1"
                icon={<Icon name="error" />}
                status={errors?.sop?.message ? 'danger' : undefined}
                message={errors?.sop?.message}
                className="w-full"
              >
                <Controller
                  name={'sop'}
                  control={control}
                  rules={{ required: 'SOP is required' }}
                  render={({ field }) => (
                    <Select
                      {...field}
                      options={dummySopOptions}
                      placeholder="Please select"
                      value={field?.value || ''}
                    />
                  )}
                />
              </Field>
            </div>
            <div className="w-full">
              <Field
                title="Stage"
                titleClassName="mb-1"
                icon={<Icon name="error" />}
                status={errors?.sop?.message ? 'danger' : undefined}
                message={errors?.sop?.message}
                className="w-full"
              >
                <Controller
                  name={'category'}
                  control={control}
                  rules={{ required: 'Stage is required' }}
                  render={({ field }) => (
                    <Select
                      {...field}
                      options={DUMMY_CATEGORY}
                      placeholder="Please select"
                      value={field?.value || ''}
                    />
                  )}
                />
              </Field>
            </div>
            <div className="w-full">
              <Field
                title="Step"
                titleClassName="mb-1"
                icon={<Icon name="error" />}
                status={errors?.sop?.message ? 'danger' : undefined}
                message={errors?.sop?.message}
                className="w-full"
              >
                <Controller
                  name={'step'}
                  control={control}
                  rules={{ required: 'Step is required' }}
                  render={({ field }) => (
                    <Select
                      {...field}
                      options={DUMMY_STEP}
                      placeholder="Please select"
                      value={field?.value || ''}
                    />
                  )}
                />
              </Field>
            </div>
          </div>
          <div className="flex flex-col gap-y-4">
            <Field
              title="Script to test"
              titleClassName="mb-1"
              icon={<Icon name="error" />}
              status={errors?.text?.message ? 'danger' : undefined}
              message={errors?.text?.message}
              className="w-full"
            >
              <Controller
                name={'text'}
                control={control}
                rules={{ required: 'Text is required' }}
                render={({ field }) => (
                  <textarea
                    className={cn(
                      'h-32 w-full border border-black-200 rounded-lg p-2'
                    )}
                    placeholder="Enter script to test"
                    {...field}
                  />
                )}
              />
            </Field>
            <Button type="submit">Test Script</Button>
          </div>
          <div className={cn('pb-6', !showResult && 'hidden')}>
            <div className="flex flex-row items-center gap-x-4 py-4 border-t border-gray-200">
              <div className="font-bold text-body">Result:</div>
              <div className="bg-status-danger px-2 py-1 text-white rounded-md">
                Failed
              </div>
            </div>
            <div className="flex flex-col gap-y-1">
              {[
                {
                  passed: true,
                  rule: 'Dictionary %_Dict_Person_Title_% exists',
                },
                {
                  passed: true,
                  rule: 'Metadata {{customer_name}} exists',
                },
                {
                  passed: true,
                  rule: 'Metadata {{number_phone_no}} equals to 54321234',
                },
                {
                  passed: false,
                  rule: 'Text matching for "請提供以下資料以確認閣下是貴公司的授權人士代表貴公司去執行此指示" > 80%',
                  reason: 'Text matching is 60%',
                },
              ]?.map((item, index) => (
                <div
                  key={`validation-rule-${index}`}
                  className="flex flex-row items-center gap-x-2"
                >
                  {item?.passed ? (
                    <Check
                      size={24}
                      className="text-status-success"
                    />
                  ) : (
                    <X
                      size={24}
                      className="text-status-danger"
                    />
                  )}
                  <span className="flex flex-row items-center gap-x-1">
                    {`${item.rule}`}
                  </span>
                  {!item?.passed && (
                    <span className="text-status-danger">({item?.reason})</span>
                  )}
                </div>
              ))}
            </div>
            <div className="mt-4 mb-2 font-bold text-body">Extractions:</div>
            <div className="flex flex-col gap-y-1">
              {[
                {
                  key: 'customer_name',
                  value: '陳大文',
                },
                {
                  key: 'number_phone_no',
                  value: '54321234',
                },
              ]?.map((item, index) => (
                <div
                  key={`extraction-${index}`}
                  className="flex flex-row items-center gap-x-2"
                >
                  <span className="font-bold">{`${item.key}:`}</span>
                  <span>{item.value}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </form>
    </FormProvider>
  );
};

export default TestRules;
