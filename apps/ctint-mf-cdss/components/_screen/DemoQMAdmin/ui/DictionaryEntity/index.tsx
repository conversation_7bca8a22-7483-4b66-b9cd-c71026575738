import Input from '@cdss-modules/design-system/components/_ui/Input';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import Field from '@cdss-modules/design-system/components/_ui/Field';
import {
  Popup,
  PopupContent,
} from '@cdss-modules/design-system/components/_ui/Popup';
import { Controller, useFieldArray, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { Minus, Plus } from 'lucide-react';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import { cn } from '@cdss-modules/design-system/lib/utils';
import { Button } from '@cdss-modules/design-system';

const dictionarySchema = yup
  .object({
    entityKey: yup.string().required('Entity Key is required'),
    name: yup.string().required('Name is required'),
    values: yup.array().of(
      yup.object({
        type: yup.string().required('Type is required'),
        value: yup.string().required('Value is required'),
      })
    ),
  })
  .required();

export type TDictionaryEntityProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
};

const DICTIONARY_ENTITY_VALUE_TYPE_OPTIONS = [
  { label: 'Text', value: 'text' },
  { label: 'Regex', value: 'regex' },
];

export const DictionaryEntity = ({
  open,
  onOpenChange,
}: TDictionaryEntityProps) => {
  const {
    handleSubmit,
    control,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(dictionarySchema),
    defaultValues: {
      values: [{ type: '', value: '' }],
    },
  });
  const { fields, append, remove } = useFieldArray({
    control, // control props comes from useForm (optional: if you are using FormProvider)
    name: 'values', // unique name for your Field Array
  });

  const onSubmit = async (data: any) => {
    window.alert(`Submitted data: ${JSON.stringify(data)}`);
  };

  const hasMultipleValues = fields.length > 1;

  return (
    <Popup
      open={open}
      onOpenChange={onOpenChange}
    >
      <PopupContent
        className="w-4/5 max-w-[700px] shadow-md"
        title="New Dictionary Entity"
      >
        <form
          onSubmit={handleSubmit(onSubmit)}
          className="flex flex-col w-full px-4 pt-4 pb-6"
        >
          {/* <div className="flex justify-between mb-4">
            <div className="w-full max-w-[200px]">
              <Button
                variant="back"
                onClick={() => {
                  onOpenChange(false);
                }}
                beforeIcon={<Icon name="back" />}
                bodyClassName="px-0 min-w-0"
                className="px-0"
              >
                &nbsp;Back
              </Button>
            </div>
          </div> */}
          <div className="flex-1 h-0 flex flex-col gap-y-4">
            <div
              className={cn('flex gap-x-4 w-full', hasMultipleValues && 'pr-8')}
            >
              <div className="w-full">
                <Field
                  title={'Entity Key'}
                  icon={<Icon name="error" />}
                  status={errors?.entityKey?.message ? 'danger' : undefined}
                  message={errors?.entityKey?.message}
                >
                  <Controller
                    name="entityKey"
                    control={control}
                    rules={{ required: true }}
                    render={({ field }) => (
                      <Input
                        size="s"
                        placeholder='No spaces, e.g. "entity-id"'
                        {...field}
                      />
                    )}
                  />
                </Field>
              </div>
              <div className="w-full">
                <Field
                  title={'Entity Name'}
                  icon={<Icon name="error" />}
                  placeholder="Enter Name"
                  status={errors?.name?.message ? 'danger' : undefined}
                  message={errors?.name?.message}
                >
                  <Controller
                    name="name"
                    control={control}
                    rules={{ required: true }}
                    render={({ field }) => (
                      <Input
                        size="s"
                        {...field}
                      />
                    )}
                  />
                </Field>
              </div>
            </div>
            <div className="flex gap-x-4 w-full">
              <Field
                title={
                  <div className="flex items-center gap-x-2">
                    <span>Entity Values</span>
                    <button
                      type="button"
                      className="bg-black hover:bg-primary text-white rounded-full size-5 flex-none flex justify-center items-center"
                      onClick={() => {
                        append({ type: '', value: '' });
                      }}
                    >
                      <Plus size={16} />
                    </button>
                  </div>
                }
                titleClassName="mb-1"
                icon={<Icon name="error" />}
                status={errors?.values?.message ? 'danger' : undefined}
                message={errors?.values?.message}
                className="w-full"
              >
                <div
                  className={cn(
                    'flex flex-col gap-y-4 max-h-[300px] overflow-auto',
                    hasMultipleValues && 'pr-8'
                  )}
                >
                  {fields.map((field, index) => (
                    <div
                      key={field.id}
                      className="relative flex gap-x-4"
                    >
                      <div className="w-full">
                        <Controller
                          name={`values.${index}.type`}
                          control={control}
                          rules={{ required: true }}
                          render={({ field }) => (
                            <Select
                              mode="single"
                              options={DICTIONARY_ENTITY_VALUE_TYPE_OPTIONS.map(
                                (item) => ({
                                  id: `from-no-${item.value}`,
                                  label: item.label,
                                  value: item?.value,
                                })
                              )}
                              {...field}
                              placeholder="Select Type"
                              labelClassName="h-full text-remark"
                              labelContainerClassName="h-8"
                              onChange={(value) => {
                                field.onChange(value);
                              }}
                            />
                          )}
                        />
                      </div>
                      <div className="w-full">
                        <Controller
                          name={`values.${index}.value`}
                          control={control}
                          rules={{ required: true }}
                          render={({ field }) => (
                            <Input
                              placeholder="Enter Value"
                              size="s"
                              {...field}
                            />
                          )}
                        />
                      </div>
                      {index > 0 && (
                        <div className="absolute left-full h-full bottom-0 flex items-end pl-2 pb-2">
                          <button
                            type="button"
                            className="bg-black hover:bg-primary text-white rounded-full p-1 size-5 flex-none flex justify-center items-center"
                            onClick={() => {
                              remove(index);
                            }}
                          >
                            <Minus size={16} />
                          </button>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </Field>
            </div>
            <div className="flex mt-2 gap-x-4 justify-between">
              {/* <Button variant="secondary">Cancel</Button> */}
              <Button type="submit">Save</Button>
            </div>
          </div>
        </form>
      </PopupContent>
    </Popup>
  );
};

export default DictionaryEntity;
