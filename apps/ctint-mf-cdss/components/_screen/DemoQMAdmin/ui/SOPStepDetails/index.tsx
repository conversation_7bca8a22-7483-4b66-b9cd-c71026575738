import React, { memo, useEffect, useState } from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { Plus } from 'lucide-react';
import SOPCriteria from '../SOPCriteria';
import SOPVerificationRule from '../SOPVerificationRule';
import SOPPassingRule from '../SOPPassingRule';
import { cn } from '@cdss-modules/design-system/lib/utils';
import SOPExtractions from '../SOPExtractions';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import Switch from '@cdss-modules/design-system/components/_ui/Switch';

const SOPStepRules = ({ control, path }: any) => {
  const {
    fields: criteriaFields,
    append: appendCriteria,
    remove: removeCriteria,
  } = useFieldArray({
    control,
    name: `${path}.criteria`,
  });
  const {
    fields: verificationRuleFields,
    append: appendVerificationRule,
    remove: removeVerificationRule,
  } = useFieldArray({
    control,
    name: `${path}.verificationRules`,
  });
  const {
    fields: extractionFields,
    append: appendExtraction,
    remove: removeExtraction,
  } = useFieldArray({
    control,
    name: `${path}.extractions`,
  });

  const [shouldExtract, setShouldExtract] = useState(true);

  return (
    <>
      <div className="relative flex flex-col gap-y-4 mb-2">
        <div className="flex justify-between items-center gap-x-2">
          <div className="flex items-center gap-x-2">
            <h4 className="font-bold text-body">Verification Rules</h4>
            <button
              type="button"
              className="bg-black hover:bg-primary text-white rounded-full size-5 flex-none flex justify-center items-center"
              onClick={() => {
                appendVerificationRule({
                  dataType: 'saved',
                  dataValue: '',
                  relation: 'exists',
                });
              }}
            >
              <Plus size={16} />
            </button>
          </div>
          <div className="flex items-center gap-2">
            <label
              className={`text-remark font-bold ${shouldExtract ? 'text-black' : 'text-grey-500'}`}
            >
              {shouldExtract ? 'Extract entities' : 'No Extraction'}
            </label>
            <Switch
              size="s"
              activeColor="green"
              // defaultChecked={shouldExtract}
              checked={shouldExtract}
              onChange={() => setShouldExtract(!shouldExtract)}
            />
          </div>
        </div>
        {verificationRuleFields.map((field, index) => (
          <div
            key={field.id}
            className="flex flex-wrap gap-4 items-end"
          >
            {/* Verification Rule Section */}
            <SOPVerificationRule
              control={control}
              path={`${path}.verificationRules[${index}]`}
              index={index}
              remove={removeVerificationRule}
              fields={verificationRuleFields}
            />
          </div>
        ))}
        {/* Passing Rule Section */}
        {verificationRuleFields?.length > 0 && (
          <div className="relative mt-4">
            <SOPPassingRule
              control={control}
              path={`${path}.passingRules`}
              label={'Step will pass if'}
              fields={verificationRuleFields}
            />
          </div>
        )}
      </div>

      {/* Criteria */}
      <div className="relative">
        <div className="flex items-center gap-x-2 mb-2">
          <h4 className="font-bold text-body">Scenarios</h4>
          <button
            type="button"
            className="bg-black hover:bg-primary text-white rounded-full size-5 flex-none flex justify-center items-center"
            onClick={() => {
              appendCriteria({
                id: `category-${Math.random()}`,
                name: '',
                steps: [],
              });
            }}
          >
            <Plus size={16} />
          </button>
        </div>
        {criteriaFields.map((criteria, index) => (
          <div
            key={criteria.id}
            className="flex justify-between items-center mb-1"
          >
            {/* Criteria Section */}
            <SOPCriteria
              control={control}
              path={`${path}.criteria[${index}]`}
              index={index}
              remove={removeCriteria}
              fields={criteriaFields}
            />
          </div>
        ))}
      </div>

      {/* Passing Rule Section */}
      {criteriaFields?.length > 0 && (
        <div className="relative">
          <SOPPassingRule
            control={control}
            path={`${path}.criteriaPassingRules`}
            label={'Step will run if'}
            fields={criteriaFields}
          />
        </div>
      )}
      {/* Extractions */}
      <div className="relative hidden">
        <div className="flex items-center gap-x-2 mb-2">
          <h4 className="font-bold text-body">Extractions</h4>
          <button
            type="button"
            className="bg-black hover:bg-primary text-white rounded-full size-5 flex-none flex justify-center items-center"
            onClick={() => {
              appendExtraction({
                id: `extractions-${Math.random()}`,
                type: 'dictionary',
                key: '',
              });
            }}
          >
            <Plus size={16} />
          </button>
        </div>
        {extractionFields.map((extraction, index) => (
          <div
            key={extraction.id}
            className="flex justify-between items-center mb-1"
          >
            {/* Extractions Section */}
            <SOPExtractions
              control={control}
              path={`${path}.extractions[${index}]`}
              index={index}
              remove={removeExtraction}
            />
          </div>
        ))}
      </div>
    </>
  );
};

const SOPStepDetails = ({ errors, path, stepIndex, removeStep }: any) => {
  const { watch, control } = useFormContext();
  const categoryPath = path.split('.').slice(0, -1).join('.');
  const participants = watch('participants');
  const [selected, setSelected] = useState('');

  useEffect(() => {
    if (selected) return;
    if (participants?.length > 0) {
      setSelected(participants[0].id);
    }
  }, [participants, selected]);

  const updateSelected = (id: string) => {
    setSelected(id);
  };
  return (
    <div
      className={cn(
        'relative p-6 rounded h-full flex flex-col gap-y-4 bg-primary-200 overflow-auto'
      )}
    >
      <div className={cn('flex flex-col gap-y-4')}>
        {/* Verification Rules */}
        <h2 className="font-bold text-t6">
          SOP / {watch(`${categoryPath}.name`)} / {watch(`${path}.name`)}
        </h2>
        <div className="flex items-center gap-x-4 border-b border-grey-300 overflow-auto -mt-2">
          {participants?.map((participant: any) => (
            <button
              type="button"
              key={participant.id}
              className={cn(
                'flex items-center gap-x-1 py-2 px-4 font-bold',
                selected === participant.id
                  ? 'shadow-tab-selected text-primary-700'
                  : ''
              )}
              onClick={() => updateSelected(participant.id)}
            >
              <Icon
                name="user"
                size={16}
              />
              <span className="text-remark whitespace-nowrap">
                {participant.name}
              </span>
            </button>
          ))}
        </div>
        {participants?.map((participant: any) => (
          <div
            key={`step-rules-${participant.id}`}
            className={cn(
              'flex flex-col gap-y-4',
              selected === participant.id ? '' : 'hidden'
            )}
          >
            <SOPStepRules
              control={control}
              path={`${path}.participants.${participant.id}`}
              remove={removeStep}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default memo(SOPStepDetails);
