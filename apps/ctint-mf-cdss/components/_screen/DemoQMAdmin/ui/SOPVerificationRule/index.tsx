/* eslint-disable no-useless-escape */
import React from 'react';
import { Controller, useFieldArray, useFormContext } from 'react-hook-form';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import Field from '@cdss-modules/design-system/components/_ui/Field';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import {
  DUMMY_DICTIONARY,
  DUMMY_META,
  DUMMY_RULES,
  DUMMY_TOPICS,
} from '../../dummy';
import { Plus, Save, Trash2 } from 'lucide-react';
import { cn } from '@cdss-modules/design-system/lib/utils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@cdss-modules/design-system/components/_ui/DropdownMenu';
import SimilarityTextarea from '../SimilarityTextarea';

const dataTypeOptions = [
  { label: 'Saved Rules', value: 'saved' },
  { label: 'Custom Rule', value: 'text' },
  // { label: 'Metadata', value: 'metadata' },
  // { label: 'Dictionary', value: 'dict' },
  { label: 'Rule Set', value: 'rule-set' },
];

const relationOptions = [
  { label: 'Exists', value: 'exists' },
  { label: 'Equals to', value: 'equals' },
  { label: '≥', value: 'ge' },
  { label: '>', value: 'gt' },
  { label: '≤', value: 'le' },
  { label: '<', value: 'lt' },
];
const ruleSetRelationOptions = [
  { label: 'All', value: 'all' },
  { label: 'Number of', value: 'numberof' },
];

const metaOptions = DUMMY_META.map((meta) => ({
  label: meta.name,
  value: `{{${meta.metaKey}}}`,
  id: meta.id,
}));

const dictionaryOptions = DUMMY_DICTIONARY.map((dict) => ({
  label: dict.name,
  value: `%_${dict.entityKey}_%`,
  id: dict.id,
}));

const topicOptions = DUMMY_TOPICS.map((topic) => ({
  label: `${topic.key} - ${topic.name}`,
  value: topic.id,
  id: topic.id,
}));

const SOPVerificationRule = ({
  control,
  path,
  index,
  remove,
  fields,
  isRuleForm,
}: any) => {
  const { watch } = useFormContext();
  const dataType = watch(`${path}.dataType`);
  const dataRelation = watch(`${path}.relation`);

  const {
    fields: sampleFields,
    append: appendSample,
    remove: removeSample,
  } = useFieldArray({
    control,
    name: `${path}.samples`,
  });

  const [metadataToAdd, setMetadataToAdd] = React.useState<string>('');
  const [dictionaryToAdd, setDictionaryToadd] = React.useState<string>('');

  const [selection, setSelection] = React.useState<any>(null);

  const addTextBasedOnPosition = (
    text: string,
    position: number,
    value: string
  ) => {
    const firstPart = text.slice(0, position);
    const secondPart = text.slice(position);
    return `${firstPart}${value}${secondPart}`;
  };

  const isCompareValue =
    dataRelation === 'equals' ||
    dataRelation === 'ge' ||
    dataRelation === 'gt' ||
    dataRelation === 'le' ||
    dataRelation === 'lt';

  return (
    <div className="flex flex-col gap-4 items-start w-full">
      <div className="w-full flex items-center gap-4">
        <div className="flex gap-4 items-center">
          <div className={cn('w-6 font-bold', isRuleForm && 'hidden')}>
            {isRuleForm ? `GR${index + 1}.` : `V${index + 1}.`}
          </div>
          <Field
            title=""
            className="flex-1"
          >
            <Controller
              name={`${path}.dataType`}
              control={control}
              rules={{ required: 'Data type is required' }}
              render={({ field }) => (
                <Select
                  {...field}
                  labelClassName="h-full text-remark"
                  labelContainerClassName="h-8"
                  options={dataTypeOptions
                    ?.filter(
                      (option) => !isRuleForm || option?.value !== 'saved'
                    )
                    ?.map((option) => ({
                      id: option.value,
                      label: option.label,
                      value: option.value,
                    }))}
                  placeholder="Data Type"
                />
              )}
            />
          </Field>
          {dataType === 'text' ? (
            <>
              <div className="flex items-center gap-x-2">
                <div>Similarity</div>
                <div>&ge;</div>
                <Field
                  title=""
                  className="w-[80px]"
                >
                  <Controller
                    name={`${path}.relationFactor`}
                    control={control}
                    rules={{ required: 'Relation factor is required', min: 0 }}
                    render={({ field }) => (
                      <Input
                        {...field}
                        size="s"
                        type="number"
                        placeholder=""
                        value={field.value || 90}
                        min="1"
                      />
                    )}
                  />
                </Field>
                <div>%</div>
              </div>
            </>
          ) : dataType === 'saved' ? (
            <Field title="">
              <Controller
                name={`${path}.dataKey`}
                control={control}
                rules={{ required: 'Rules is required' }}
                render={({ field }) => (
                  <Select
                    {...field}
                    labelClassName="h-full text-remark"
                    labelContainerClassName="h-8"
                    showSearch={true}
                    options={DUMMY_RULES.map((rule) => ({
                      id: rule.id,
                      label: `${rule.name}`,
                      value: rule.id,
                    }))}
                    placeholder="Rules"
                  />
                )}
              />
            </Field>
          ) : (
            <>
              {dataType === 'metadata' && (
                <Field title="">
                  <Controller
                    name={`${path}.dataKey`}
                    control={control}
                    rules={{ required: 'Metadata is required' }}
                    render={({ field }) => (
                      <Select
                        {...field}
                        labelClassName="h-full text-remark"
                        labelContainerClassName="h-8"
                        showSearch={true}
                        options={metaOptions}
                        placeholder="Metadata"
                      />
                    )}
                  />
                </Field>
              )}
              {dataType === 'dict' && (
                <Field title="">
                  <Controller
                    name={`${path}.dataKey`}
                    control={control}
                    rules={{ required: 'Dictionary is required' }}
                    render={({ field }) => (
                      <Select
                        {...field}
                        labelClassName="h-full text-remark"
                        labelContainerClassName="h-8"
                        showSearch={true}
                        options={dictionaryOptions}
                        placeholder="Dictionary"
                      />
                    )}
                  />
                </Field>
              )}
              <Field title="">
                <Controller
                  name={`${path}.relation`}
                  control={control}
                  rules={{ required: 'Relation is required' }}
                  defaultValue={relationOptions[0].value}
                  render={({ field }) => (
                    <Select
                      {...field}
                      labelClassName="h-full text-remark"
                      labelContainerClassName="h-8"
                      options={(dataType === 'rule-set'
                        ? ruleSetRelationOptions
                        : relationOptions
                      )?.map((option) => ({
                        id: option.value,
                        label: option.label,
                        value: option.value,
                      }))}
                      placeholder="Relation"
                    />
                  )}
                />
              </Field>
              {dataType === 'rule-set' && dataRelation === 'numberof' && (
                <>
                  <Field
                    title=""
                    className="w-[80px]"
                  >
                    <Controller
                      name={`${path}.relationFactor`}
                      control={control}
                      rules={{
                        required: 'Number of rules is required',
                        min: 1,
                      }}
                      render={({ field }) => (
                        <Input
                          {...field}
                          type="number"
                          placeholder="Num."
                          size="s"
                          min="1"
                        />
                      )}
                    />
                  </Field>
                </>
              )}
              {dataType === 'rule-set' && (
                <>
                  <div>of</div>
                  <div className="w-[200px]">
                    <Controller
                      name={`${path}.ruleSet`}
                      control={control}
                      render={({ field }) => (
                        <Select
                          {...field}
                          labelClassName="h-full text-remark"
                          labelContainerClassName="h-8"
                          placeholder="Select rules"
                          mode="multiple"
                          showSearch={true}
                          options={
                            new Array(fields?.length || 0)
                              ?.fill(0)
                              ?.filter((_, i) => i !== index)
                              ?.map((_, i) => ({
                                id: `Rule V${i + 1}`,
                                label: `Rule V${i + 1}`,
                                value: `Rule V${i + 1}`,
                              })) || []
                          }
                          onChange={(e) => {
                            const isSelected = e?.target.checked;
                            const value = e?.target.value;
                            const prevValues = field?.value || [];

                            if (isSelected) {
                              field?.onChange([...prevValues, value]);
                            } else {
                              field?.onChange(
                                prevValues?.filter(
                                  (prev: string) => prev !== value
                                )
                              );
                            }
                          }}
                          value={field?.value || []}
                        />
                      )}
                    />
                  </div>
                  <div>passed.</div>
                </>
              )}

              {isCompareValue && (
                <Field title="">
                  <Controller
                    name={`${path}.dataValue`}
                    control={control}
                    rules={{ required: 'Data value is required' }}
                    render={({ field }) => (
                      <Input
                        {...field}
                        size="s"
                        placeholder="Enter Data Value"
                      />
                    )}
                  />
                </Field>
              )}
            </>
          )}
        </div>
        {dataType !== 'saved' && !isRuleForm && (
          <button
            type="button"
            onClick={() => null}
          >
            <Save size={20} />
          </button>
        )}
        {remove && (
          <button
            type="button"
            onClick={() => remove(index)}
          >
            <Trash2 size={20} />
          </button>
        )}
      </div>
      {dataType === 'text' && (
        <>
          <SimilarityTextarea
            control={control}
            path={`${path}`}
            isRuleForm={isRuleForm}
          />
          <div className={cn('w-full')}>
            {sampleFields?.length > 0 && (
              <div className="w-full flex flex-col gap-3 mb-2">
                <div className="text-body font-bold">
                  Examples {`(${sampleFields.length})`}
                </div>
                <div className="w-full flex flex-col gap-4 mb-2">
                  {sampleFields?.map((sample, i) => {
                    return (
                      <div
                        className="flex gap-x-4"
                        key={sample.id}
                      >
                        <div className="font-bold inline-flex w-6 h-8 items-center flex-none">{`E${i + 1}.`}</div>
                        {watch(`${path}.samples[${i}].type`) === 'topic' ? (
                          <div className="w-full max-w-[150px]">
                            <Controller
                              name={`samples[${i}].value`}
                              control={control}
                              rules={{ required: 'Topic is required' }}
                              defaultValue={relationOptions[0].value}
                              render={({ field }) => (
                                <div className="relative w-full flex gap-x-3 group">
                                  <Select
                                    showSearch
                                    options={topicOptions}
                                    labelClassName="h-full text-remark"
                                    labelContainerClassName="h-8"
                                    placeholder="Select a Topic"
                                    {...field}
                                  />
                                  <button
                                    type="button"
                                    className="hover:text-primary-600 transition-all opacity-0 group-hover:opacity-100"
                                    onClick={() => removeSample(i)}
                                  >
                                    <Trash2 size={20} />
                                  </button>
                                </div>
                              )}
                            />
                          </div>
                        ) : (
                          <div className="relative w-full flex gap-x-3 group">
                            <Controller
                              name={`samples[${i}].value`}
                              control={control}
                              rules={{ required: 'Sample is required' }}
                              render={({ field }) => (
                                <textarea
                                  {...field}
                                  className={cn(
                                    'w-full h-[90px] border rounded-md p-2 pr-8 focus:outline-none focus:border-primary-900 focus:shadow-field resize-none',
                                    field.value
                                      ? 'border-black'
                                      : 'border-gray-300'
                                  )}
                                  placeholder="Enter sample for topics"
                                  value={field.value}
                                />
                              )}
                            />
                            <button
                              type="button"
                              className="absolute right-3 top-3 hover:text-primary-600 transition-all opacity-0 group-hover:opacity-100"
                              onClick={() => removeSample(i)}
                            >
                              <Trash2 size={20} />
                            </button>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
            <DropdownMenu>
              <DropdownMenuTrigger>
                <button
                  type="button"
                  className="w-full flex items-center gap-x-1 transition-all rounded-md opacity-40 hover:opacity-100 hover:text-prmary"
                >
                  <div className="text-black hover:text-primary flex justify-center items-center">
                    <Plus size={20} />
                  </div>
                  Add Example
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem
                  className="text-remark"
                  onClick={() => {
                    appendSample({
                      type: 'topic',
                      value: '',
                    });
                  }}
                >
                  From Topic
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="text-remark"
                  onClick={() => {
                    appendSample({
                      type: 'text',
                      value: '',
                    });
                  }}
                >
                  New Example
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </>
      )}
    </div>
  );
};

export default SOPVerificationRule;
