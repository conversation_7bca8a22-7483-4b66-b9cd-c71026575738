// pages/api/user.ts

import type { NextApiRequest, NextApiResponse } from 'next';


type DataResponse = {
  data: {
    result: string;
  } | null;
  isSuccess: boolean;
  error: any;
};


export default function handler(
  req: NextApiRequest,
  res: NextApiResponse<DataResponse>
) {
  const body = req.body;

  try {
    res.status(200).json({
      data: {
        result: body,
      },
      isSuccess: true,
      error: null,
    });
  } catch (error) {
    // If decryption fails or sourceId does not match
    res.status(403).json({
      data: null,
      isSuccess: false,
      error: 'Failed to decrypt token or token data is invalid.',
    });
  }
}