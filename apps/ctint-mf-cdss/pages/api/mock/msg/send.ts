// pages/api/user.ts

import type { NextApiRequest, NextApiResponse } from 'next';

const DUMMY_DATA = {
  data: {
    categoryList: [
      {
        pkey: 'AllowAgent',
        code: 'AllowAgent',
        dbUser: 'sa',
        name: 'Allow Agent Access',
        parentCode: 'WhatsappTemplatePermission',
        status: 'ACTIVE',
        createUserCode: 'SYSTEM',
        createDatetime: '2024-03-20T08:55:01.805Z',
        latestUpdateUserCode: null,
        latestUpdateDatetime: null,
      },
      {
        pkey: 'AllowSupervisor',
        code: 'AllowSupervisor',
        dbUser: 'sa',
        name: 'Only Allow Supervisor Access',
        parentCode: 'WhatsappTemplatePermission',
        status: 'ACTIVE',
        createUserCode: 'SYSTEM',
        createDatetime: '2024-03-20T08:55:01.805Z',
        latestUpdateUserCode: null,
        latestUpdateDatetime: null,
      },
    ],
    categoryTree: {
      AllowSupervisor: {},
      AllowAgent: {},
    },
    messageTemplateList: [
      {
        pkey: 'a_promotion_en_US',
        code: 'a_promotion_en_US',
        dbUser: 'sa',
        name: 'a_promotion (en_US)',
        category: 'AllowSupervisor',
        lockCounter: 22,
        messageJson:
          '[{"body":{"text":"Shop now through {{1}} and use code {{2}} to get {{3}} off of all merchandise."},"option":null,"image":null,"language":"en_US"}]',
        messageParameters:
          '[{"id":"1","defaultValue":"the end of August"},{"id":"2","defaultValue":"25OFF"},{"id":"3","defaultValue":"25%"}]',
        isOffline: true,
        realTemplateId: 'a_promotion',
        status: 'ACTIVE',
        createUserCode: 'SYSTEM',
        createDatetime: '2024-03-07T05:45:36.310Z',
        latestUpdateUserCode: 'SYSTEM',
        latestUpdateDatetime: '2024-03-26T07:10:40.527Z',
        channelType: 'WHATSAPP',
        channelAccountNumber: '***********',
        templateType: 'STANDARD',
        messageCategory: 'MARKETING',
        language: 'en_US',
        approvalStatus: 'APPROVED',
        rejectedReason: null,
        messageJsonHtml:
          '[{"body":{"text":"Shop now through {{1}} and use code {{2}} to get {{3}} off of all merchandise."},"option":null,"image":null,"language":"en_US"}]',
      },
      {
        pkey: 'ansel_test_2_image_header_en_en',
        code: 'ansel_test_2_image_header_en_en',
        dbUser: 'sa',
        name: 'ansel_test_2_image_header_en (en)',
        category: 'AllowSupervisor',
        lockCounter: 22,
        messageJson:
          '[{"body":{"text":"test"},"option":{"type":"BUTTON","list":[{"id":0,"type":"URL","text":"更多關於繳付海外運費","label":"更多關於繳付海外運費","content":"https://hktv.my.site.com/cs/s/article/Ship-to-Overseas-Add-a-Oversea-Delivery-Information?language=zh_TW"}]},"image":{"fileName":"placeholder.png"},"language":"en"}]',
        messageParameters: '[]',
        isOffline: true,
        realTemplateId: 'ansel_test_2_image_header_en',
        status: 'ACTIVE',
        createUserCode: 'SYSTEM',
        createDatetime: '2024-03-07T05:45:36.684Z',
        latestUpdateUserCode: 'SYSTEM',
        latestUpdateDatetime: '2024-03-26T07:10:40.621Z',
        channelType: 'WHATSAPP',
        channelAccountNumber: '***********',
        templateType: 'MEDIA',
        messageCategory: 'UTILITY',
        language: 'en',
        approvalStatus: 'REJECTED',
        rejectedReason: null,
        messageJsonHtml:
          '[{"body":{"text":"test"},"option":{"type":"BUTTON","list":[{"id":0,"type":"URL","text":"更多關於繳付海外運費","label":"更多關於繳付海外運費","content":"https://hktv.my.site.com/cs/s/article/Ship-to-Overseas-Add-a-Oversea-Delivery-Information?language=zh_TW"}]},"image":{"fileName":"placeholder.png"},"language":"en"}]',
      },
      {
        pkey: 'ansel_test_2_image_header_zh_hk_zh_HK',
        code: 'ansel_test_2_image_header_zh_hk_zh_HK',
        dbUser: 'sa',
        name: 'ansel_test_2_image_header_zh_hk (zh_HK)',
        category: 'AllowSupervisor',
        lockCounter: 22,
        messageJson:
          '[{"body":{"text":"你好啊😊 見到您尋日落左張直送英國既訂單，您既訂單將有專人免費重新包裝，以減低海外運費📦💰 並於3日內運送到物流公司倉庫。 海外運送則由ShipEazy物流公司負責。到時我哋會於「訊息」內通知您到物流公司網站填寫英國送貨地址和支付海外運費💁🏻&zwj;♀️Tracking Number一樣可以經「訊息」度揾到~ 提提您: 農曆年間的購買流程會比平時稍長🧧 唔想錯過任何優惠，即刻加入我地既Whatsapp爆買團🛍️ chat.whatsapp.com/DWX5rfFS8fa7u1E4vrQmh9 如果有問題，歡迎您隨時搵我哋 CS Team幫手丫😊 有關物流情況，可隨時聯絡 ShipEazy CS 可進入網上LiveChat tawk.to/chat/627257aa7b967b11798dbb2d/1g27actuf 填寫查詢表格 或電郵 <EMAIL>"},"option":{"type":"BUTTON","list":[{"id":0,"type":"URL","text":"更多關於繳付海外運費","label":"更多關於繳付海外運費","content":"https://hktv.my.site.com/cs/s/article/Ship-to-Overseas-Add-a-Oversea-Delivery-Information?language=zh_TW"}]},"image":{"fileName":"placeholder.png"},"language":"zh_HK"}]',
        messageParameters: '[]',
        isOffline: true,
        realTemplateId: 'ansel_test_2_image_header_zh_hk',
        status: 'ACTIVE',
        createUserCode: 'SYSTEM',
        createDatetime: '2024-03-07T05:45:36.745Z',
        latestUpdateUserCode: 'SYSTEM',
        latestUpdateDatetime: '2024-03-26T07:10:40.667Z',
        channelType: 'WHATSAPP',
        channelAccountNumber: '***********',
        templateType: 'MEDIA',
        messageCategory: 'MARKETING',
        language: 'zh_HK',
        approvalStatus: 'REJECTED',
        rejectedReason: null,
        messageJsonHtml:
          '[{"body":{"text":"你好啊😊 見到您尋日落左張直送英國既訂單，您既訂單將有專人免費重新包裝，以減低海外運費📦💰 並於3日內運送到物流公司倉庫。 海外運送則由ShipEazy物流公司負責。到時我哋會於「訊息」內通知您到物流公司網站填寫英國送貨地址和支付海外運費💁🏻&zwj;♀️Tracking Number一樣可以經「訊息」度揾到~ 提提您: 農曆年間的購買流程會比平時稍長🧧 唔想錯過任何優惠，即刻加入我地既Whatsapp爆買團🛍️ chat.whatsapp.com/DWX5rfFS8fa7u1E4vrQmh9 如果有問題，歡迎您隨時搵我哋 CS Team幫手丫😊 有關物流情況，可隨時聯絡 ShipEazy CS 可進入網上LiveChat tawk.to/chat/627257aa7b967b11798dbb2d/1g27actuf 填寫查詢表格 或電郵 <EMAIL>"},"option":{"type":"BUTTON","list":[{"id":0,"type":"URL","text":"更多關於繳付海外運費","label":"更多關於繳付海外運費","content":"https://hktv.my.site.com/cs/s/article/Ship-to-Overseas-Add-a-Oversea-Delivery-Information?language=zh_TW"}]},"image":{"fileName":"placeholder.png"},"language":"zh_HK"}]',
      },
    ],
  },
};

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader(
    'Access-Control-Allow-Methods',
    'GET, POST, PUT, DELETE, OPTIONS'
  );
  res.setHeader(
    'Access-Control-Allow-Headers',
    'X-Requested-With, Content-Type, Accept'
  );

  try {
    // If everything is okay, return the user data
    res.status(200).json(DUMMY_DATA);
  } catch (error) {
    // If decryption fails or sourceId does not match
    res.status(403).json({
      data: null,
      isSuccess: false,
      error: 'Failed to get template data',
    });
  }
}
