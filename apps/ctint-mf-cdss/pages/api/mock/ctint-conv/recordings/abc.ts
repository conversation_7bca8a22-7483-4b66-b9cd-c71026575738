// pages/api/user.ts

import type { NextApiRequest, NextApiResponse } from 'next';

type DataResponse = {
  data: any;
  isSuccess: boolean;
  totalCount?: number;
  error: any;
};

const DUMMY_DATA = {
  id: 'E0DBMNLAM52MJ0GUEKVQ8I0ODO00000T',
  conversationId: '-',
  startTime: '2024-03-22T02:57:28.000+0000',
  endTime: '2024-03-22T02:58:08.000+0000',
  mediaUri:
    '/recordings/E0DBMNLAM52MJ0GUEKVQ8I0ODO00000T/play/a5d659f4-c5c7-472e-9a7a-f9dcdafc44cf.mp3',
  duration: 40.187,
  users: null,
  direction: 'Internal',
  mediaType: 'call',
  username: 'MOCK USER',
  dialedNumber: '22001',
  callerNumber: '71002',
  transcript: [
    {
      start: '0.000',
      end: '5.000',
      speaker: 'Agent',
      text: '你好，今日是 2024年八月十七日，就您打算認購的股票掛鈎產品開始錄音。<mark>我是銀行職員。</mark>',
    },
    {
      start: '5.100',
      end: '10.000',
      speaker: 'Agent',
      text: '陳大欣小姐，您的全名是陳大欣。請您提供全名、身份證號碼後面的 4 位數字及以下其中 2 項資料核對身份：出生日期（日子及月份）、住址或公司地址。',
    },
    {
      start: '10.100',
      end: '12.000',
      speaker: 'Customer',
      text: '我叫 陳大欣，身份證號碼後四位是5678，住址是香港鰂魚涌456號7樓8室。',
    },
    {
      start: '12.100',
      end: '16.000',
      speaker: 'Agent',
      text: '有關您的弱勢客戶評估，您表示在本行以外有複雜產品及其他產品的投資經驗。加上本行紀錄，您的評估結果為非弱勢客戶。',
    },
    {
      start: '16.100',
      end: '21.000',
      speaker: 'Agent',
      text: '本行建議，您應由「客戶見證人」陪同協助您明白產品內容及風險，並進行有關投資。客戶見證人 XXX 先生/小姐是您的朋友，並確認是 65 歲以下、教育水平是中學程度或以上，並確認對此產品有認識及有足夠理解能力協助您了解此交易。',
    },
    {
      start: '21.100',
      end: '23.000',
      speaker: 'Customer',
      text: '我確認這些資訊是正確的。',
    },
    {
      start: '23.100',
      end: '28.000',
      speaker: 'Agent',
      text: '您向本行作出聲明確認您並非首次購買此產品類別，及已有此類投資產品經驗，是不受「落單冷靜期」安排所覆蓋。請確認。',
    },
    {
      start: '28.100',
      end: '30.000',
      speaker: 'Customer',
      text: '是的，我確認這些資料。',
    },
    {
      start: '30.100',
      end: '35.000',
      speaker: 'Agent',
      text: '我們現在進行產品適合性評估。您是否有足夠時間考慮投資適合性，明白及接受此產品之產品的特質、運作模式、相關風險、潛在損失及確定進行交易? 請確認。',
    },
    {
      start: '35.100',
      end: '37.000',
      speaker: 'Customer',
      text: '是的，我已經考慮充分並接受這些風險。',
    },
    {
      start: '37.100',
      end: '42.000',
      speaker: 'Agent',
      text: '根據產品適合性評估，您會預留多於 6 個月但少於或等如 12 個月的家庭開支備用現金或高流動性資產。在本行已擁有此類產品的投資經驗。是次投資金額 CCY。股票掛鈎投資戶口。現金結算戶口。當此申請獲接納便不可撤回，如果您接受交易，銀行會即時凍結資金。您是否確認此申請?',
    },
    {
      start: '42.100',
      end: '44.000',
      speaker: 'Customer',
      text: '我確認所有資訊並願意繼續進行。',
    },
    {
      start: '44.100',
      end: '46.000',
      speaker: 'Agent',
      text: '非常感謝您的申請，我們會盡快通知您此產品的發行結果。錄音完成，多謝您的申請。',
    },
  ],
};

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse<DataResponse>
) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader(
    'Access-Control-Allow-Methods',
    'GET, POST, PUT, DELETE, OPTIONS'
  );
  res.setHeader('Access-Control-Allow-Headers', '*');
  try {
    // If everything is okay, return the user data
    res.status(200).json({
      data: [DUMMY_DATA],
      isSuccess: true,
      error: null,
    });
  } catch (error) {
    // If decryption fails or sourceId does not match
    res.status(403).json({
      data: null,
      isSuccess: false,
      error: 'Failed to get single recording data',
    });
  }
}
