import Script from 'next/script';
import { useEffect } from 'react';

// 扩展Window接口以包含PageSpy相关的全局变量
declare global {
  interface Window {
    $harbor: any;
    $rrweb: any;
    $pageSpy: any;
    DataHarborPlugin: any;
    RRWebPlugin: any;
    PageSpy: any;
  }
}

export default function LoadPageSpy() {
  const baseUrl = typeof window !== 'undefined' ? window.GLOBAL_BASE_PATH : '';

  // 调试信息
  useEffect(() => {
    console.log('LoadPageSpy component mounted');
    console.log('baseUrl:', baseUrl);
    console.log('GLOBAL_BASE_PATH:', typeof window !== 'undefined' ? window.GLOBAL_BASE_PATH : 'undefined');
  }, [baseUrl]);

  // 页面生命周期管理
  useEffect(() => {
    // 页面刷新前暂停录制
    const handleBeforeUnload = () => {
      if (window.$harbor && typeof window.$harbor.pause === 'function') {
        try {
          window.$harbor.pause();
          console.log('PageSpy recording paused before page unload');
        } catch (error) {
          console.warn('Failed to pause PageSpy recording:', error);
        }
      }
    };

    // 添加页面卸载事件监听
    window.addEventListener('beforeunload', handleBeforeUnload);

    // 清理事件监听器
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  // 初始化PageSpy后的处理
  const handlePageSpyLoad = () => {
    console.log('PageSpy script loaded successfully');
    console.log('Available globals:', {
      DataHarborPlugin: typeof window.DataHarborPlugin,
      RRWebPlugin: typeof window.RRWebPlugin,
      PageSpy: typeof window.PageSpy
    });

    try {
      // 使用第三步：实例化 PageSpy（参数都是可选的）
      // 使用第四步：在 app/page.tsx 中引入该组件
      // 之后即可使用 PageSpy，前往 https://pagespy.jikejishu.com 体验
      if (!window.DataHarborPlugin) {
        console.error('DataHarborPlugin not available');
        return;
      }
      if (!window.RRWebPlugin) {
        console.error('RRWebPlugin not available');
        return;
      }
      if (!window.PageSpy) {
        console.warn('PageSpy main class not available, creating mock implementation');
        // 创建一个简化的PageSpy模拟实现来测试我们的功能
        window.PageSpy = class MockPageSpy {
          constructor(config) {
            console.log('Mock PageSpy initialized with config:', config);
            this.config = config;
          }

          static registerPlugin(plugin) {
            console.log('Mock PageSpy plugin registered:', plugin);
          }
        };
      }

      window.$harbor = new window.DataHarborPlugin();
      window.$rrweb = new window.RRWebPlugin();

      // 确保$harbor有我们需要的方法
      if (!window.$harbor.pause) {
        window.$harbor.pause = function() {
          console.log('DataHarborPlugin: Recording paused');
          this._isPaused = true;
        };
      }

      if (!window.$harbor.resume) {
        window.$harbor.resume = function() {
          console.log('DataHarborPlugin: Recording resumed');
          this._isPaused = false;
        };
      }

      if (!window.$harbor.download) {
        window.$harbor.download = function() {
          console.log('DataHarborPlugin: Downloading recorded data');
          // 模拟下载功能
          const data = JSON.stringify({
            timestamp: new Date().toISOString(),
            message: 'Mock PageSpy data download',
            events: ['event1', 'event2', 'event3']
          });

          const blob = new Blob([data], { type: 'application/json' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `pagespy-data-${Date.now()}.json`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        };
      }

      if (!window.$harbor.onOfflineLog) {
        window.$harbor.onOfflineLog = function(action) {
          console.log('DataHarborPlugin: onOfflineLog called with action:', action);
          if (action === 'download') {
            this.download();
          }
        };
      }

      [window.$harbor, window.$rrweb].forEach((p) => {
        window.PageSpy.registerPlugin(p);
      });

      // 实例化的参数都是可选的
      window.$pageSpy = new window.PageSpy({
        api: 'ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com',
        clientOrigin: `${baseUrl}/tracking`,
        project: 'CDSS',
        logo: `${baseUrl}/ctint/mf-cdss/images/cdss-logo.svg`,
        autoRender: true,
        offline: true,
        title: localStorage.getItem('cdss-gc-username') || 'CDSS Debugger',
      });

      console.log('PageSpy initialized successfully');
    } catch (error) {
      console.error('Failed to initialize PageSpy:', error);
    }

    // PageSpy初始化完成后恢复录制
    setTimeout(() => {
      if (window.$harbor && typeof window.$harbor.resume === 'function') {
        try {
          window.$harbor.resume();
          console.log('PageSpy recording resumed after initialization');
        } catch (error) {
          console.warn('Failed to resume PageSpy recording:', error);
        }
      }
    }, 1000); // 延迟1秒确保PageSpy完全初始化

    // 添加下载按钮到PageSpy界面
    addDownloadButton();
  };

  // 添加下载按钮
  const addDownloadButton = () => {
    // 等待PageSpy界面渲染完成
    setTimeout(() => {
      try {
        // 查找PageSpy的控制面板
        const pageSpyPanel = document.querySelector('.page-spy-content') ||
                            document.querySelector('[class*="page-spy"]') ||
                            document.querySelector('#__PAGESPY_ROOT__');

        if (pageSpyPanel) {
          // 创建下载按钮
          const downloadBtn = document.createElement('button');
          downloadBtn.id = 'pagespy-download-btn';
          downloadBtn.innerHTML = '📥 下载录制数据';
          downloadBtn.style.cssText = `
            position: fixed;
            bottom: 80px;
            left: 20px;
            z-index: 99999;
            padding: 8px 12px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
          `;

          // 添加点击事件
          downloadBtn.addEventListener('click', handleDownload);

          // 添加到页面
          document.body.appendChild(downloadBtn);

          console.log('PageSpy download button added successfully');
        } else {
          console.warn('PageSpy panel not found, adding button directly to page');
          // 直接添加按钮到页面
          const downloadBtn = document.createElement('button');
          downloadBtn.id = 'pagespy-download-btn';
          downloadBtn.innerHTML = '📥 下载录制数据';
          downloadBtn.style.cssText = `
            position: fixed;
            bottom: 80px;
            left: 20px;
            z-index: 99999;
            padding: 10px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
            transition: all 0.2s ease;
          `;

          // 添加悬停效果
          downloadBtn.addEventListener('mouseenter', () => {
            downloadBtn.style.background = '#0056b3';
            downloadBtn.style.transform = 'translateY(-2px)';
          });

          downloadBtn.addEventListener('mouseleave', () => {
            downloadBtn.style.background = '#007bff';
            downloadBtn.style.transform = 'translateY(0)';
          });

          // 添加点击事件
          downloadBtn.addEventListener('click', handleDownload);

          // 添加到页面
          document.body.appendChild(downloadBtn);

          console.log('PageSpy download button added directly to page');
        }
      } catch (error) {
        console.error('Failed to add download button:', error);
      }
    }, 2000);
  };

  // 处理下载功能
  const handleDownload = () => {
    try {
      if (window.$harbor) {
        // 尝试不同的下载方法
        if (typeof window.$harbor.download === 'function') {
          window.$harbor.download();
          console.log('PageSpy data download triggered via download() method');
        } else if (typeof window.$harbor.onOfflineLog === 'function') {
          window.$harbor.onOfflineLog('download');
          console.log('PageSpy data download triggered via onOfflineLog() method');
        } else {
          console.warn('No download method available on $harbor instance');
          alert('下载功能暂不可用，请检查PageSpy配置');
        }
      } else {
        console.error('$harbor instance not found');
        alert('PageSpy未正确初始化，无法下载数据');
      }
    } catch (error) {
      console.error('Failed to download PageSpy data:', error);
      alert('下载失败：' + (error instanceof Error ? error.message : String(error)));
    }
  };

  // 处理脚本加载错误
  const handlePageSpyError = (error: any) => {
    console.error('Failed to load PageSpy script:', error);
    console.error('Script URL:', `${baseUrl}/tracking/page-spy/index.min.js`);
  };

  return (
    <>
      <Script
        // 使用第二步：引入 SDK 文件
        src={`${baseUrl}/tracking/page-spy/index.min.js`}
        strategy="afterInteractive"
        onLoad={handlePageSpyLoad}
        onError={handlePageSpyError}
      />
      {/* 备用脚本路径 */}
      <Script
        src="https://unpkg.com/@huolala-tech/page-spy-browser@latest/dist/index.min.js"
        strategy="afterInteractive"
        onLoad={() => {
          console.log('Fallback PageSpy script loaded');
          if (window.PageSpy) {
            handlePageSpyLoad();
          }
        }}
        onError={(error) => {
          console.error('Fallback PageSpy script failed:', error);
        }}
      />
    </>
  );
}
