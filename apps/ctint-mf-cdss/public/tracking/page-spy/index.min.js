// PageSpy Mock Implementation for Local Development
// This is a simplified mock implementation for testing purposes
// In production, you should use the actual PageSpy library

(function() {
  'use strict';
  
  // Mock PageSpy implementation
  window.PageSpy = class PageSpy {
    constructor(config = {}) {
      this.config = config;
      this.plugins = [];
      console.log('PageSpy initialized with config:', config);
      
      // Auto render if enabled
      if (config.autoRender !== false) {
        this.render();
      }
    }
    
    static registerPlugin(plugin) {
      console.log('PageSpy plugin registered:', plugin);
    }
    
    render() {
      console.log('PageSpy render called');
      // Create a simple debug panel
      this.createDebugPanel();
    }
    
    createDebugPanel() {
      // Remove existing panel if any
      const existingPanel = document.getElementById('pagespy-debug-panel');
      if (existingPanel) {
        existingPanel.remove();
      }
      
      // Create debug panel
      const panel = document.createElement('div');
      panel.id = 'pagespy-debug-panel';
      panel.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 60px;
        height: 60px;
        background: #007bff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 12px;
        font-weight: bold;
        cursor: pointer;
        z-index: 99998;
        box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        transition: all 0.2s ease;
      `;
      panel.innerHTML = 'PS';
      panel.title = 'PageSpy Debug Panel';
      
      // Add hover effect
      panel.addEventListener('mouseenter', () => {
        panel.style.transform = 'scale(1.1)';
      });
      
      panel.addEventListener('mouseleave', () => {
        panel.style.transform = 'scale(1)';
      });
      
      // Add click handler
      panel.addEventListener('click', () => {
        this.togglePanel();
      });
      
      document.body.appendChild(panel);
    }
    
    togglePanel() {
      console.log('PageSpy panel toggled');
      // In a real implementation, this would show/hide the debug panel
      alert('PageSpy Debug Panel\n\n功能已集成：\n✅ 页面刷新时自动暂停/恢复录制\n✅ 下载录制数据功能\n✅ 生命周期管理');
    }
    
    abort() {
      console.log('PageSpy aborted');
      const panel = document.getElementById('pagespy-debug-panel');
      if (panel) {
        panel.remove();
      }
    }
  };
  
  console.log('PageSpy mock implementation loaded successfully');
})();
